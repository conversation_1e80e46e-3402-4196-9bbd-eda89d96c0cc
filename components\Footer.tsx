'use client';

import Link from 'next/link';
import { Mail, Phone, MapPin, Linkedin } from 'lucide-react';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const footerLinks = {
    courses: [
      { name: 'Python Level 1', href: '/courses/python-level1' },
      { name: 'Python Level 2', href: '/courses/python-level2' },
      { name: 'Python Level 3', href: '/courses/python-level3' },
      { name: 'Basics of Robotics', href: '/courses/basics-robotics' },
      { name: 'Machine Learning', href: '/courses/ml-algorithms' },
      { name: 'View All Courses', href: '/courses' },
    ],
    company: [
      { name: 'About Us', href: '/about' },
      { name: 'Meet Our Founder', href: '/about' },
      { name: 'Student Projects', href: '/projects' },
      { name: 'Contact Us', href: '/contact' },
    ],
    quickLinks: [
      { name: 'Home', href: '/' },
      { name: 'Courses', href: '/courses' },
      { name: 'Projects', href: '/projects' },
      { name: 'About', href: '/about' },
      { name: 'Contact', href: '/contact' },
    ],
  };

  const socialLinks = [
    { name: 'Founder Portfolio', icon: Linkedin, href: 'https://iamproloy.vercel.app/' },
    { name: 'Contact Us', icon: Mail, href: '/contact' },
  ];

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <div className="flex items-center mb-6">
              <div className="h-10 w-10 bg-gradient-to-r from-blue-600 to-emerald-500 rounded-lg flex items-center justify-center">
                <img
                  src="/logo.svg"
                  alt="dotPY Academy Logo"
                  className="h-7 w-7"
                  onError={(e) => {
                    // Show fallback text if logo doesn't load
                    e.currentTarget.style.display = 'none';
                    const fallback = e.currentTarget.parentElement?.querySelector('.logo-fallback') as HTMLElement;
                    if (fallback) fallback.style.display = 'block';
                  }}
                />
                <span className="text-white font-bold text-xl logo-fallback" style={{ display: 'none' }}>D</span>
              </div>
              <span className="ml-3 text-xl font-bold">dotPY Academy</span>
            </div>
            <p className="text-gray-300 mb-6 text-sm leading-relaxed">
              Empowering the next generation with cutting-edge technology education.
              Learn Python programming, robotics, and machine learning from expert instructors.
            </p>

            {/* Mission Statement */}
            <div className="mb-6">
              <h4 className="text-white font-semibold mb-3">Our Mission</h4>
              <p className="text-gray-400 text-sm">
                To make technology education accessible, engaging, and transformative for young minds aged 6-20.
              </p>
            </div>

            {/* Contact Info */}
            <div className="space-y-2">
              <div className="flex items-center text-gray-300">
                <Mail className="h-4 w-4 mr-2 text-blue-400" />
                <a href="mailto:<EMAIL>" className="hover:text-blue-400 transition-colors duration-200 text-sm">
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-center text-gray-300">
                <Phone className="h-4 w-4 mr-2 text-blue-400" />
                <a href="tel:+8801770065234" className="hover:text-blue-400 transition-colors duration-200 text-sm">
                  +8801770065234
                </a>
              </div>
              <div className="flex items-center text-gray-300">
                <MapPin className="h-4 w-4 mr-2 text-blue-400" />
                <span className="text-sm">Gulshan 1, Dhaka, Bangladesh</span>
              </div>
            </div>
          </div>

          {/* Courses */}
          <div>
            <h3 className="text-base font-semibold mb-3">Our Courses</h3>
            <ul className="space-y-1">
              {footerLinks.courses.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-blue-400 transition-colors duration-200 text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Company */}
          <div>
            <h3 className="text-base font-semibold mb-3">Academy</h3>
            <ul className="space-y-1">
              {footerLinks.company.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-blue-400 transition-colors duration-200 text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-base font-semibold mb-3">Quick Links</h3>
            <ul className="space-y-1">
              {footerLinks.quickLinks.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-blue-400 transition-colors duration-200 text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Course Info */}
          <div>
            <h3 className="text-base font-semibold mb-3">Course Info</h3>
            <div className="space-y-2">
              <div className="text-gray-300 text-sm">
                <span className="text-blue-400 font-medium">Age Groups:</span>
                <br />6-20 years
              </div>
              <div className="text-gray-300 text-sm">
                <span className="text-blue-400 font-medium">Class Size:</span>
                <br />Max 8 students
              </div>
              <div className="text-gray-300 text-sm">
                <span className="text-blue-400 font-medium">Duration:</span>
                <br />13-26 weeks
              </div>
              <div className="text-gray-300 text-sm">
                <span className="text-blue-400 font-medium">Schedule:</span>
                <br />Flexible timings
              </div>
              <div className="text-gray-300 text-sm">
                <span className="text-blue-400 font-medium">Demo Class:</span>
                <br />FREE available
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-700 mt-8 pt-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-gray-400 text-xs mb-2 md:mb-0">
              © {currentYear} dotPY Academy. All rights reserved. Founded by Polok Poddar.
            </div>

            {/* Social Links */}
            <div className="flex space-x-2">
              {socialLinks.map((social) => (
                <a
                  key={social.name}
                  href={social.href}
                  target={social.href.startsWith('http') ? '_blank' : '_self'}
                  rel={social.href.startsWith('http') ? 'noopener noreferrer' : undefined}
                  className="text-gray-400 hover:text-blue-400 transition-colors duration-200 p-1 hover:bg-gray-800 rounded-lg flex items-center space-x-1"
                  aria-label={social.name}
                >
                  <social.icon className="h-4 w-4" />
                  <span className="text-xs hidden sm:inline">{social.name}</span>
                </a>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;

