'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, CheckCircle, Users, Calendar, DollarSign, BookOpen } from 'lucide-react';

interface EnrollmentFormProps {
  isOpen: boolean;
  onClose: () => void;
  selectedCourse?: {
    id: string;
    title: string;
    price: number;
    originalPrice: number;
    duration: string;
    ageGroup: string;
  } | null;
}

export default function EnrollmentForm({ isOpen, onClose, selectedCourse }: EnrollmentFormProps) {
  const [showThankYou, setShowThankYou] = useState(false);
  const [isFormLoaded, setIsFormLoaded] = useState(false);

  // Listen for form submission completion
  useEffect(() => {
    if (!isOpen) {
      setShowThankYou(false);
      setIsFormLoaded(false);
      return;
    }

    // Listen for messages from the iframe (Google Forms)
    const handleMessage = (event: MessageEvent) => {
      // Google Forms sends a message when form is submitted
      if (event.data && typeof event.data === 'string') {
        if (event.data.includes('formResponse') || event.data.includes('submitted')) {
          setShowThankYou(true);
        }
      }
    };

    window.addEventListener('message', handleMessage);
    
    // Auto-show thank you after 30 seconds (fallback)
    const thankYouTimer = setTimeout(() => {
      if (isOpen && !showThankYou) {
        // Check if user might have submitted (simple heuristic)
        setShowThankYou(true);
      }
    }, 30000);

    return () => {
      window.removeEventListener('message', handleMessage);
      clearTimeout(thankYouTimer);
    };
  }, [isOpen, showThankYou]);

  const handleClose = () => {
    setShowThankYou(false);
    setIsFormLoaded(false);
    onClose();
  };

  const handleThankYouClose = () => {
    setShowThankYou(false);
    setTimeout(() => {
      handleClose();
    }, 500);
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 overflow-y-auto">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-50"
          onClick={handleClose}
        />

        {/* Modal */}
        <div className="flex min-h-full items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative w-full max-w-4xl bg-white rounded-2xl shadow-2xl"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">
                  Course Enrollment
                </h2>
                {selectedCourse && (
                  <p className="text-gray-600 mt-1">
                    Enrolling for: <span className="font-medium text-blue-600">{selectedCourse.title}</span>
                  </p>
                )}
              </div>
              <button
                onClick={handleClose}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
              >
                <X className="h-6 w-6 text-gray-500" />
              </button>
            </div>

            {/* Course Info (if selected) */}
            {selectedCourse && (
              <div className="p-6 bg-blue-50 border-b border-gray-200">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="flex items-center">
                    <BookOpen className="h-5 w-5 text-blue-600 mr-2" />
                    <div>
                      <p className="text-xs text-gray-500">Course</p>
                      <p className="font-medium text-gray-900">{selectedCourse.title}</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Users className="h-5 w-5 text-blue-600 mr-2" />
                    <div>
                      <p className="text-xs text-gray-500">Age Group</p>
                      <p className="font-medium text-gray-900">{selectedCourse.ageGroup}</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Calendar className="h-5 w-5 text-blue-600 mr-2" />
                    <div>
                      <p className="text-xs text-gray-500">Duration</p>
                      <p className="font-medium text-gray-900">{selectedCourse.duration}</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <DollarSign className="h-5 w-5 text-blue-600 mr-2" />
                    <div>
                      <p className="text-xs text-gray-500">Price</p>
                      <div className="flex items-center space-x-2">
                        <p className="font-bold text-green-600">৳{selectedCourse.price.toLocaleString()}</p>
                        <p className="text-xs text-gray-500 line-through">৳{selectedCourse.originalPrice.toLocaleString()}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Form Content */}
            <div className="relative">
              <AnimatePresence mode="wait">
                {showThankYou ? (
                  // Thank You Message
                  <motion.div
                    key="thankyou"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="p-12 text-center"
                  >
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                      className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6"
                    >
                      <CheckCircle className="h-10 w-10 text-green-600" />
                    </motion.div>
                    
                    <motion.h3
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.4 }}
                      className="text-2xl font-bold text-gray-900 mb-4"
                    >
                      Thank You for Your Enrollment! 🎉
                    </motion.h3>
                    
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.6 }}
                      className="space-y-3 text-gray-600 mb-8"
                    >
                      <p className="text-lg">
                        Your enrollment request has been submitted successfully!
                      </p>
                      <p>
                        Our team will contact you within <strong>24 hours</strong> to confirm your enrollment and provide further details.
                      </p>
                      {selectedCourse && (
                        <div className="bg-blue-50 p-4 rounded-lg mt-4">
                          <p className="text-blue-800">
                            <strong>Course:</strong> {selectedCourse.title}<br />
                            <strong>Price:</strong> ৳{selectedCourse.price.toLocaleString()} (Save ৳{(selectedCourse.originalPrice - selectedCourse.price).toLocaleString()})
                          </p>
                        </div>
                      )}
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.8 }}
                      className="space-y-4"
                    >
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="font-medium text-gray-900 mb-2">What's Next?</h4>
                        <ul className="text-sm text-gray-600 space-y-1">
                          <li>✅ We'll review your enrollment request</li>
                          <li>📞 Our team will call you to confirm details</li>
                          <li>💳 Payment instructions will be provided</li>
                          <li>🚀 You'll receive class schedule and materials</li>
                        </ul>
                      </div>

                      <div className="flex flex-col sm:flex-row gap-3 justify-center">
                        <button
                          onClick={handleThankYouClose}
                          className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                        >
                          Continue Exploring
                        </button>
                        <a
                          href="/"
                          className="px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium text-center"
                        >
                          Back to Home
                        </a>
                      </div>
                    </motion.div>
                  </motion.div>
                ) : (
                  // Google Form
                  <motion.div
                    key="form"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="relative"
                  >
                    {/* Loading overlay */}
                    {!isFormLoaded && (
                      <div className="absolute inset-0 bg-white flex items-center justify-center z-10">
                        <div className="text-center">
                          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                          <p className="text-gray-600">Loading enrollment form...</p>
                        </div>
                      </div>
                    )}
                    
                    {/* Google Form Iframe */}
                    <iframe
                      src="https://docs.google.com/forms/d/e/1FAIpQLSdEFRBYJXQnH6Tyl79KYaavFOy1w8ZuHzL4MrdJR3_6k7Sr3g/viewform?embedded=true"
                      width="100%"
                      height="600"
                      frameBorder="0"
                      marginHeight={0}
                      marginWidth={0}
                      onLoad={() => setIsFormLoaded(true)}
                      className="w-full"
                      title="Course Enrollment Form"
                    >
                      Loading enrollment form...
                    </iframe>

                    {/* Manual Thank You Trigger */}
                    <div className="p-4 bg-gray-50 border-t text-center">
                      <p className="text-sm text-gray-600 mb-3">
                        Submitted the form? Click below to see confirmation:
                      </p>
                      <button
                        onClick={() => setShowThankYou(true)}
                        className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-medium"
                      >
                        I've Submitted the Form
                      </button>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </motion.div>
        </div>
      </div>
    </AnimatePresence>
  );
}
