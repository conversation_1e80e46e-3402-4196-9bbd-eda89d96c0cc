'use client';

import { useState } from 'react';
import Hero from '../components/Hero';
import FeaturedCourses from '../components/FeaturedCourses';
import FounderSection from '../components/FounderSection';
import StudentProjects from '../components/StudentProjects';
import WorkshopGallery from '../components/WorkshopGallery';
import WhyChooseUs from '../components/WhyChooseUs';
import DemoClassForm from '../components/DemoClassForm';
import EnrollmentForm from '../components/EnrollmentForm';

export default function Home() {
  const [isDemoFormOpen, setIsDemoFormOpen] = useState(false);
  const [isEnrollmentFormOpen, setIsEnrollmentFormOpen] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState(null);

  const handleEnrollClick = (course: any) => {
    setSelectedCourse(course);
    setIsEnrollmentFormOpen(true);
  };

  return (
    <main className="min-h-screen">
      <Hero
        onDemoClick={() => setIsDemoFormOpen(true)}
        onEnrollClick={() => setIsEnrollmentFormOpen(true)}
      />
      <FeaturedCourses onEnrollClick={handleEnrollClick} />
      <FounderSection />
      <StudentProjects />
      <WorkshopGallery />
      <WhyChooseUs />

      {/* Demo Class Form */}
      <DemoClassForm
        isOpen={isDemoFormOpen}
        onClose={() => setIsDemoFormOpen(false)}
      />

      {/* Enrollment Form */}
      <EnrollmentForm
        isOpen={isEnrollmentFormOpen}
        onClose={() => setIsEnrollmentFormOpen(false)}
        selectedCourse={selectedCourse}
      />
    </main>
  );
}
