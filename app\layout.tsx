import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import Navbar from '../components/Navbar'
import Footer from '../components/Footer'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'dotPY Academy - Professional Programming Education',
  description: 'Learn Python programming, robotics, and machine learning from expert instructors. Structured courses for ages 6-20.',
  keywords: 'Python programming, robotics, machine learning, coding education, programming courses, STEM education',
  authors: [{ name: 'dotPY Academy' }],
  openGraph: {
    title: 'dotPY Academy - Professional Programming Education',
    description: 'Learn Python programming, robotics, and machine learning from expert instructors.',
    type: 'website',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes" />
      </head>
      <body className={`${inter.className} no-overflow`}>
        <Navbar />
        <main className="min-h-screen">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  )
}
