# 🚀 Quick Deploy to cPanel - dotPY Academy

## ✅ **YOUR FILES ARE READY!**

Your website has been built and all files are in the `out` folder. Here's exactly what to do:

---

## 📁 **WHAT TO UPLOAD**

**Upload ALL these files from your `out` folder to your cPanel `public_html` folder:**

```
📂 out/ (Upload everything inside this folder)
├── 📄 index.html (Homepage)
├── 📄 404.html (Error page)
├── 📁 _next/ (CSS & JavaScript - IMPORTANT!)
├── 📁 about/ (About page)
├── 📁 contact/ (Contact page)
├── 📁 courses/ (All course pages)
├── 📁 projects/ (Projects page)
├── 📁 videos/ (Video files)
├── 📁 workshop/ (Workshop images)
├── 🖼️ founder.jpg (Founder photo)
└── 🖼️ logo.svg (Logo)
```

---

## 🎯 **3-MINUTE DEPLOYMENT**

### **Step 1: Login to cPanel (1 minute)**
1. Go to your hosting control panel
2. Click "File Manager"
3. Navigate to `public_html` folder

### **Step 2: Upload Files (2 minutes)**
1. Click "Upload" button
2. Select ALL files from your `out` folder
3. Wait for upload to complete
4. Extract if needed

### **Step 3: Test (30 seconds)**
1. Visit your domain: `yourdomain.com`
2. Check if homepage loads correctly
3. Test navigation menu

---

## ✅ **VERIFICATION CHECKLIST**

After upload, verify these URLs work:

- [ ] **Homepage**: `yourdomain.com`
- [ ] **About**: `yourdomain.com/about/`
- [ ] **Courses**: `yourdomain.com/courses/`
- [ ] **Python Level 1**: `yourdomain.com/courses/python-level1/`
- [ ] **Projects**: `yourdomain.com/projects/`
- [ ] **Contact**: `yourdomain.com/contact/`

---

## 🎉 **THAT'S IT!**

Your dotPY Academy website is now LIVE! 

### **Features Working:**
✅ Professional design
✅ All course information
✅ Student projects showcase
✅ Contact form (display)
✅ Mobile responsive
✅ Fast loading

### **Optional Next Steps:**
- Set up email auto-reply (see EMAIL_SETUP_GUIDE.md)
- Add Google Analytics
- Set up SSL certificate
- Update social media links

**Your coding academy website is ready to attract students! 🚀**
