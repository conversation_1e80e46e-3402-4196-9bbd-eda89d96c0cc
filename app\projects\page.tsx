'use client';

import { motion } from 'framer-motion';
import { Award, Code, Cpu, Play, ArrowLeft } from 'lucide-react';
import Link from 'next/link';

export default function ProjectsPage() {
  const projects = [
    {
      id: 1,
      title: "Temperature Monitoring System",
      description: "An advanced Arduino-based temperature monitoring system that displays real-time temperature readings on an LCD screen. This project demonstrates sensor integration, data processing, and user interface design. Students learn to work with temperature sensors, LCD displays, and create alert systems for temperature thresholds.",
      videoSrc: "/videos/robotics project _ working with temperature arduino and display.mp4",
      category: "Robotics",
      level: "Advanced",
      student: "Advanced Robotics Student",
      duration: "3 weeks",
      skills: ["Arduino Programming", "Sensor Integration", "LCD Display", "Temperature Monitoring", "Alert Systems", "Data Processing"],
      learningOutcomes: [
        "Understanding sensor interfacing with microcontrollers",
        "LCD display programming and control",
        "Real-time data processing and display",
        "Creating threshold-based alert systems"
      ]
    },
    {
      id: 2,
      title: "LED Pattern Controller",
      description: "A creative LED light pattern controller using Arduino programming with loops and timing functions. This project showcases dynamic visual effects and teaches students about timing control, loop structures, and creative programming. Perfect for understanding basic electronics and programming concepts.",
      videoSrc: "/videos/robotics projects LED user different time use loop.mp4",
      category: "Robotics",
      level: "Beginner",
      student: "Basics of Robotics Student",
      duration: "2 weeks",
      skills: ["Arduino Programming", "LED Control", "Loop Functions", "Timing Control", "Pattern Design", "Basic Electronics"],
      learningOutcomes: [
        "Mastering loop structures in programming",
        "Understanding timing and delay functions",
        "LED circuit design and control",
        "Creative problem-solving through code"
      ]
    },
    {
      id: 3,
      title: "Water Level Detection System",
      description: "A smart water level monitoring system using advanced sensors and Arduino for automated water management. This project demonstrates IoT concepts, sensor technology, and automation principles. Students learn to create practical solutions for real-world problems.",
      videoSrc: "/videos/robotics projects working with arduino and water lavel test.mp4",
      category: "Robotics",
      level: "Advanced",
      student: "Advanced Robotics Student",
      duration: "4 weeks",
      skills: ["Sensor Technology", "Arduino Programming", "Water Level Detection", "Automation", "IoT Concepts", "System Integration"],
      learningOutcomes: [
        "Advanced sensor interfacing and calibration",
        "Automation system design and implementation",
        "IoT concepts and connectivity",
        "Real-world problem-solving applications"
      ]
    },
    {
      id: 4,
      title: "Advanced Water Level Monitor",
      description: "An enhanced water level testing system with improved accuracy and real-time monitoring capabilities. This advanced project incorporates multiple sensors, data logging, and sophisticated monitoring algorithms for precise water level management.",
      videoSrc: "/videos/water_level_test_robotics projects.mp4",
      category: "Robotics",
      level: "Advanced",
      student: "Advanced Robotics Student",
      duration: "5 weeks",
      skills: ["Advanced Sensors", "Data Processing", "Real-time Monitoring", "System Integration", "Algorithm Design", "Data Logging"],
      learningOutcomes: [
        "Multi-sensor system integration",
        "Advanced data processing techniques",
        "Real-time monitoring system design",
        "Algorithm optimization and testing"
      ]
    },
    {
      id: 5,
      title: "dotPY Academy Student Showcase",
      description: "A comprehensive showcase of various student projects and achievements at dotPY Academy. This video highlights the diverse range of projects our students create, from basic programming exercises to advanced robotics systems, demonstrating the progression of learning and skill development.",
      videoSrc: "/videos/dotPY Academy- Posts.mp4",
      category: "Showcase",
      level: "All Levels",
      student: "Multiple Students",
      duration: "Ongoing",
      skills: ["Python Programming", "Robotics", "Project Presentation", "Innovation", "Problem Solving", "Creative Thinking"],
      learningOutcomes: [
        "Comprehensive skill development across multiple domains",
        "Project presentation and communication skills",
        "Innovation and creative problem-solving",
        "Collaborative learning and peer inspiration"
      ]
    }
  ];


  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center mb-4">
            <Link href="/" className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors duration-200">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Home
            </Link>
          </div>
          <div className="text-center">
            <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 text-sm font-medium rounded-full mb-4">
              <Award className="h-4 w-4 mr-2" />
              Student Achievements
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Student Projects Gallery
            </h1>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Explore the amazing projects created by our students. From basic programming concepts to advanced robotics systems,
              see how our students apply their learning to solve real-world problems.
            </p>
          </div>
        </div>
      </div>

      {/* Projects */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="space-y-16">
          {projects.map((project, index) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className={`grid lg:grid-cols-2 gap-12 items-center ${
                index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''
              }`}
            >
              {/* Video */}
              <div className={`${index % 2 === 1 ? 'lg:col-start-2' : ''}`}>
                <div className="relative aspect-video bg-gray-100 rounded-xl overflow-hidden shadow-lg">
                  <video
                    className="w-full h-full object-cover"
                    controls
                    muted
                    preload="metadata"
                  >
                    <source src={project.videoSrc} type="video/mp4" />
                    Your browser does not support the video tag.
                  </video>

                  {/* Play overlay for better UX */}
                  <div className="absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none">
                    <div className="bg-white/90 rounded-full p-4">
                      <Play className="h-8 w-8 text-gray-900" />
                    </div>
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className={`${index % 2 === 1 ? 'lg:col-start-1' : ''}`}>
                <div className="flex items-center gap-3 mb-4">
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                    project.category === 'Robotics'
                      ? 'bg-blue-100 text-blue-800'
                      : 'bg-emerald-100 text-emerald-800'
                  }`}>
                    {project.category}
                  </span>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                    project.level === 'Beginner'
                      ? 'bg-green-100 text-green-800'
                      : project.level === 'Advanced'
                      ? 'bg-red-100 text-red-800'
                      : 'bg-purple-100 text-purple-800'
                  }`}>
                    {project.level}
                  </span>
                </div>

                <h2 className="text-3xl font-bold text-gray-900 mb-4">
                  {project.title}
                </h2>

                <p className="text-gray-600 mb-6 leading-relaxed">
                  {project.description}
                </p>

                {/* Project Details */}
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div>
                    <div className="text-sm font-medium text-gray-900">Student</div>
                    <div className="text-sm text-gray-600">{project.student}</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-900">Duration</div>
                    <div className="text-sm text-gray-600">{project.duration}</div>
                  </div>
                </div>

                {/* Skills */}
                <div className="mb-6">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Skills Demonstrated</h4>
                  <div className="flex flex-wrap gap-2">
                    {project.skills.map((skill, skillIndex) => (
                      <span
                        key={skillIndex}
                        className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-md"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Learning Outcomes */}
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Learning Outcomes</h4>
                  <ul className="space-y-2">
                    {project.learningOutcomes.map((outcome, outcomeIndex) => (
                      <li key={outcomeIndex} className="flex items-start">
                        <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                        <span className="text-sm text-gray-600">{outcome}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-blue-50 to-emerald-50 rounded-2xl p-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Ready to Create Your Own Projects?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Join our students and start building amazing projects that solve real-world problems.
              From basic programming to advanced robotics, we'll guide you every step of the way.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/courses" className="btn-primary inline-flex items-center">
                <Cpu className="h-5 w-5 mr-2" />
                Explore Courses
              </Link>
              <Link href="/contact" className="btn-secondary inline-flex items-center">
                Get Started Today
              </Link>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}

