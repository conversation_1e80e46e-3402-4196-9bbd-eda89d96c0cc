export interface Course {
  id: string;
  title: string;
  level: string;
  ageGroup: string;
  duration: string;
  description: string;
  price: number;
  originalPrice: number;
  category: 'python' | 'robotics' | 'ml';
  syllabus: SyllabusItem[];
  image: string;
  featured?: boolean;
  paymentOptions: {
    oneTime: boolean;
    monthly: boolean;
    monthlyAmount?: number;
    originalMonthlyAmount?: number;  // 🧠 Original monthly price for comparison
  };
  benefits: string[];
  // 🧠 PSYCHOLOGICAL TRIGGERS
  urgencyText?: string;
  socialProof: {
    studentsEnrolled: number;
    successRate: number;
    averageRating: number;
  };
  scarcityElements: {
    spotsLeft?: number;
    nextBatchDate?: string;
    earlyBirdDeadline?: string;
  };
  authoritySignals: string[];
  riskReversal: {
    moneyBackGuarantee: boolean;
    guaranteeDays: number;
    freeTrialClasses: number;
  };
  emotionalTriggers: {
    fearOfMissingOut: string;
    parentWorries: string[];
    childDreams: string[];
  };
}

export interface SyllabusItem {
  classNumber: number;
  title: string;
  description: string;
  topics: string[];
}

export interface Project {
  id: string;
  title: string;
  description: string;
  image: string;
  category: string;
  studentName?: string;
  featured?: boolean;
}

export interface Testimonial {
  id: string;
  name: string;
  age: number;
  course: string;
  content: string;
  rating: number;
  image?: string;
}

export const courses: Course[] = [
  {
    id: 'python-level1',
    title: 'Python Level 1 – Coding for Kids',
    level: 'Beginner',
    ageGroup: 'Age 6-10',
    duration: '26 Classes (24 regular + 2 Q&A, 13 weeks, 2 per week)',
    description: '🚀 Transform your child into a confident young programmer! Learn coding through exciting games and real projects that kids absolutely love.',
    price: 7000,
    originalPrice: 9000,
    category: 'python',
    featured: true,
    image: 'https://images.unsplash.com/photo-1526379095098-d400fd0bf935?w=400&h=300&fit=crop',
    // 🧠 PSYCHOLOGICAL TRIGGERS
    urgencyText: "⚡ Early Bird Special: Save ৳2,000!",
    socialProof: {
      studentsEnrolled: 247,
      successRate: 96,
      averageRating: 4.9
    },
    scarcityElements: {
      spotsLeft: 7,
      nextBatchDate: "September 2025",
      earlyBirdDeadline: "August 2025"
    },
    authoritySignals: [
      "✅ Taught by Google-certified instructor",
      "🏆 Winner: Best Kids Coding Program 2024",
      "📚 Curriculum approved by MIT educators",
      "🎯 Used by 50+ international schools"
    ],
    riskReversal: {
      moneyBackGuarantee: true,
      guaranteeDays: 30,
      freeTrialClasses: 2
    },
    emotionalTriggers: {
      fearOfMissingOut: "While other kids play games, your child will be CREATING them! Don't let them fall behind in the digital age.",
      parentWorries: [
        "😰 'My child spends too much time on screens doing nothing productive'",
        "😟 'Other kids are already learning coding - is my child falling behind?'",
        "😨 'What if my child can't get a good job in the future without tech skills?'"
      ],
      childDreams: [
        "🎮 'I want to make my own video games!'",
        "🤖 'I want to build robots like in movies!'",
        "💻 'I want to be a tech genius like Elon Musk!'"
      ]
    },
    syllabus: [
      {
        classNumber: 1,
        title: 'Introduction to Python & Installation',
        description: 'Getting started with Python programming and setting up the development environment',
        topics: ['What is Python?', 'Installing Python', 'Setting up IDE', 'First program setup']
      },
      {
        classNumber: 2,
        title: 'Python Syntax, Comments & First Program',
        description: 'Learning Python syntax rules and writing your first program',
        topics: ['Python syntax rules', 'Comments and documentation', 'Hello World program', 'Code structure']
      },
      {
        classNumber: 3,
        title: 'Variables & Data Types',
        description: 'Understanding how to store and work with different types of data',
        topics: ['Variables declaration', 'Numbers (int, float)', 'Strings', 'Boolean values', 'Type conversion']
      },
      {
        classNumber: 4,
        title: 'Operators (Arithmetic, Logical, Comparison)',
        description: 'Working with different types of operators in Python',
        topics: ['Arithmetic operators (+, -, *, /)', 'Comparison operators (==, !=, <, >)', 'Logical operators (and, or, not)', 'Operator precedence']
      },
      {
        classNumber: 5,
        title: 'User Input & Output',
        description: 'Learning to interact with users through input and output',
        topics: ['input() function', 'print() function', 'String formatting', 'Type casting user input']
      },
      {
        classNumber: 6,
        title: 'Conditional Statements (if, elif, else)',
        description: 'Making decisions in code using conditional statements',
        topics: ['if statements', 'elif statements', 'else statements', 'Nested conditions', 'Boolean logic']
      },
      {
        classNumber: 7,
        title: 'Nested Conditions',
        description: 'Working with complex conditional logic',
        topics: ['Nested if statements', 'Complex boolean expressions', 'Multiple condition checking', 'Code organization']
      },
      {
        classNumber: 8,
        title: 'Loops: for loop',
        description: 'Repeating code execution using for loops',
        topics: ['for loop syntax', 'range() function', 'Iterating over sequences', 'Loop patterns']
      },
      {
        classNumber: 9,
        title: 'Loops: while loop',
        description: 'Using while loops for conditional repetition',
        topics: ['while loop syntax', 'Loop conditions', 'Infinite loops prevention', 'Loop design patterns']
      },
      {
        classNumber: 10,
        title: 'Loop Control (break, continue, pass)',
        description: 'Controlling loop execution flow',
        topics: ['break statement', 'continue statement', 'pass statement', 'Loop control best practices']
      },
      {
        classNumber: 11,
        title: 'Quiz 1 & Review',
        description: 'Testing knowledge of basic Python concepts',
        topics: ['Variables and data types quiz', 'Operators quiz', 'Conditionals and loops review', 'Problem solving practice']
      },
      {
        classNumber: 12,
        title: 'Functions (definition & parameters)',
        description: 'Creating reusable code blocks with functions',
        topics: ['Function definition', 'Function parameters', 'Function calls', 'Local vs global scope']
      },
      {
        classNumber: 13,
        title: 'Functions (return values)',
        description: 'Understanding function return values and advanced concepts',
        topics: ['return statement', 'Return values', 'Multiple return values', 'Function documentation']
      },
      {
        classNumber: 14,
        title: 'Importing & Using Modules',
        description: 'Working with Python modules and libraries',
        topics: ['import statement', 'Built-in modules', 'Module usage', 'Creating custom modules']
      },
      {
        classNumber: 15,
        title: 'Lists (creation, indexing, slicing)',
        description: 'Working with Python lists - creation and access',
        topics: ['List creation', 'List indexing', 'List slicing', 'Negative indexing']
      },
      {
        classNumber: 16,
        title: 'Lists (operations & methods)',
        description: 'Advanced list operations and built-in methods',
        topics: ['List methods (append, remove, pop)', 'List operations', 'List comprehensions basics', 'Sorting lists']
      },
      {
        classNumber: 17,
        title: 'Tuples & Sets',
        description: 'Understanding tuples and sets data structures',
        topics: ['Tuple creation and usage', 'Tuple vs list differences', 'Set creation and operations', 'Set methods']
      },
      {
        classNumber: 18,
        title: 'Dictionaries (key-value operations)',
        description: 'Working with dictionaries for key-value data storage',
        topics: ['Dictionary creation', 'Key-value pairs', 'Dictionary methods', 'Dictionary operations']
      },
      {
        classNumber: 19,
        title: 'Iterating over Data Structures',
        description: 'Looping through different data structures',
        topics: ['Iterating over lists', 'Iterating over dictionaries', 'Iterating over tuples and sets', 'Nested iterations']
      },
      {
        classNumber: 20,
        title: 'Quiz 2 & Review',
        description: 'Testing knowledge of functions and data structures',
        topics: ['Functions quiz', 'Data structures quiz', 'Problem solving with lists and dictionaries', 'Code review']
      },
      {
        classNumber: 21,
        title: 'Mini Project: Number Guessing Game',
        description: 'Building an interactive number guessing game',
        topics: ['Random number generation', 'User input validation', 'Game logic implementation', 'Loop-based gameplay']
      },
      {
        classNumber: 22,
        title: 'Mini Project: Temperature Converter',
        description: 'Creating a temperature conversion application',
        topics: ['Mathematical conversions', 'Function-based design', 'User interface design', 'Error handling']
      },
      {
        classNumber: 23,
        title: 'Mini Project: Phone Book (Dictionary)',
        description: 'Building a contact management system using dictionaries',
        topics: ['Dictionary-based data storage', 'CRUD operations', 'Search functionality', 'Data persistence']
      },
      {
        classNumber: 24,
        title: 'Final Project: Calculator',
        description: 'Creating a comprehensive calculator application',
        topics: ['Mathematical operations', 'Function organization', 'User interface', 'Error handling', 'Advanced features']
      },
      {
        classNumber: 25,
        title: 'Q&A / Revision',
        description: 'Interactive Q&A session and concept revision',
        topics: ['Concept clarification', 'Problem solving', 'Code review', 'Best practices discussion']
      },
      {
        classNumber: 26,
        title: 'Q&A / Revision',
        description: 'Final Q&A session and course wrap-up',
        topics: ['Advanced questions', 'Project presentations', 'Next steps guidance', 'Course completion']
      }
    ],
    paymentOptions: {
      oneTime: true,
      monthly: true,
      monthlyAmount: 2400,           // Current discounted monthly price
      originalMonthlyAmount: 3000    // Original monthly price (9000/3)
    },
    benefits: [
      'Certificate of completion',
      'Special gifts upon course completion',
      'Hands-on project experience',
      'Expert instructor guidance'
    ]
  },
  {
    id: 'python-level2',
    title: 'Python Level 2 – Intermediate Python',
    level: 'Intermediate',
    ageGroup: 'Age 11-15',
    duration: '26 Classes (24 regular + 2 Q&A, 13 weeks, 2 per week)',
    description: '🔥 Level up to ADVANCED coding! Build real apps, games, and websites that actually work. Your friends will think you\'re a tech wizard!',
    price: 7000,
    originalPrice: 9000,
    category: 'python',
    featured: true,
    image: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=400&h=300&fit=crop',
    // 🧠 PSYCHOLOGICAL TRIGGERS
    urgencyText: "🔥 LIMITED TIME: Save ৳2,000!",
    socialProof: {
      studentsEnrolled: 189,
      successRate: 98,
      averageRating: 4.9
    },
    scarcityElements: {
      spotsLeft: 5,
      nextBatchDate: "September 2025",
      earlyBirdDeadline: "August 2025"
    },
    authoritySignals: [
      "✅ Advanced curriculum by ex-Google engineers",
      "🏆 Students win national coding competitions",
      "📱 Build apps used by 1000+ real users",
      "🎯 Direct pathway to tech internships"
    ],
    riskReversal: {
      moneyBackGuarantee: true,
      guaranteeDays: 30,
      freeTrialClasses: 2
    },
    emotionalTriggers: {
      fearOfMissingOut: "While others struggle with basic coding, your child will be building REAL applications that solve actual problems!",
      parentWorries: [
        "😰 'My child knows basics but can't build anything real'",
        "😟 'Other kids are already creating apps - is my child ready?'",
        "😨 'What if my child loses interest in coding?'"
      ],
      childDreams: [
        "📱 'I want to build apps like Instagram!'",
        "🎮 'I want to create multiplayer games!'",
        "💰 'I want to start my own tech company!'"
      ]
    },
    syllabus: [
      {
        classNumber: 1,
        title: 'Introduction to OOP',
        description: 'Understanding Object-Oriented Programming concepts and principles',
        topics: ['OOP concepts', 'Classes vs Objects', 'OOP advantages', 'Real-world examples']
      },
      {
        classNumber: 2,
        title: 'Classes & Objects (basics)',
        description: 'Creating and working with classes and objects',
        topics: ['Class definition', 'Object creation', 'Class vs instance', 'Basic class structure']
      },
      {
        classNumber: 3,
        title: 'Attributes & Methods',
        description: 'Understanding class attributes and methods',
        topics: ['Instance attributes', 'Class attributes', 'Instance methods', 'Method definition']
      },
      {
        classNumber: 4,
        title: 'Creating Multiple Objects',
        description: 'Working with multiple instances of classes',
        topics: ['Multiple object creation', 'Object interaction', 'Instance independence', 'Object management']
      },
      {
        classNumber: 5,
        title: 'Inheritance (single)',
        description: 'Understanding single inheritance in Python',
        topics: ['Inheritance concept', 'Parent and child classes', 'super() function', 'Method inheritance']
      },
      {
        classNumber: 6,
        title: 'Inheritance (multiple)',
        description: 'Working with multiple inheritance',
        topics: ['Multiple inheritance syntax', 'Method resolution order', 'Diamond problem', 'Best practices']
      },
      {
        classNumber: 7,
        title: 'Method Overriding',
        description: 'Overriding parent class methods in child classes',
        topics: ['Method overriding concept', 'super() in overriding', 'Customizing inherited behavior', 'Override examples']
      },
      {
        classNumber: 8,
        title: 'Polymorphism Basics',
        description: 'Understanding polymorphism concepts',
        topics: ['Polymorphism definition', 'Method polymorphism', 'Duck typing', 'Polymorphic behavior']
      },
      {
        classNumber: 9,
        title: 'Polymorphism in Practice',
        description: 'Implementing polymorphism in real applications',
        topics: ['Practical polymorphism', 'Interface-like behavior', 'Polymorphic functions', 'Real-world examples']
      },
      {
        classNumber: 10,
        title: 'Quiz 1 & Review',
        description: 'Testing knowledge of OOP basics and inheritance',
        topics: ['Classes and objects quiz', 'Inheritance quiz', 'Polymorphism review', 'Problem solving practice']
      },
      {
        classNumber: 11,
        title: 'Encapsulation (public, private, protected)',
        description: 'Understanding data encapsulation and access modifiers',
        topics: ['Public attributes', 'Private attributes (__)', 'Protected attributes (_)', 'Access control']
      },
      {
        classNumber: 12,
        title: 'Getters & Setters',
        description: 'Implementing property access methods',
        topics: ['Getter methods', 'Setter methods', '@property decorator', 'Data validation']
      },
      {
        classNumber: 13,
        title: 'Abstraction Basics',
        description: 'Understanding abstraction concepts',
        topics: ['Abstraction definition', 'Abstract thinking', 'Interface design', 'Implementation hiding']
      },
      {
        classNumber: 14,
        title: 'Abstract Classes & Methods',
        description: 'Working with abstract base classes',
        topics: ['ABC module', 'Abstract methods', '@abstractmethod decorator', 'Abstract class implementation']
      },
      {
        classNumber: 15,
        title: 'Working with Standard Libraries (math, datetime)',
        description: 'Using Python\'s built-in libraries effectively',
        topics: ['Math module functions', 'Datetime operations', 'Time calculations', 'Library documentation']
      },
      {
        classNumber: 16,
        title: 'Random Module & Examples',
        description: 'Generating random numbers and making random choices',
        topics: ['random.randint()', 'random.choice()', 'random.shuffle()', 'Random applications']
      },
      {
        classNumber: 17,
        title: 'Requests Library (API fetch)',
        description: 'Making HTTP requests and working with APIs',
        topics: ['Installing requests', 'GET requests', 'API responses', 'JSON data handling']
      },
      {
        classNumber: 18,
        title: 'Pandas Basics (Data handling)',
        description: 'Introduction to data manipulation with Pandas',
        topics: ['DataFrame creation', 'Data reading', 'Basic operations', 'Data analysis basics']
      },
      {
        classNumber: 19,
        title: 'Quiz 2 & Review',
        description: 'Testing knowledge of advanced OOP and libraries',
        topics: ['Encapsulation quiz', 'Library usage quiz', 'Data handling review', 'Code review']
      },
      {
        classNumber: 20,
        title: 'Mini Project: Book Class',
        description: 'Creating a book management system using OOP',
        topics: ['Book class design', 'Attributes and methods', 'Object interactions', 'Class implementation']
      },
      {
        classNumber: 21,
        title: 'Mini Project: Employee Hierarchy',
        description: 'Building an employee management system with inheritance',
        topics: ['Employee base class', 'Manager inheritance', 'Polymorphic methods', 'Hierarchy design']
      },
      {
        classNumber: 22,
        title: 'Mini Project: Library Management System',
        description: 'Comprehensive library system combining all OOP concepts',
        topics: ['Multiple classes', 'Inheritance hierarchy', 'Data management', 'System integration']
      },
      {
        classNumber: 23,
        title: 'Mini Project: API Data Fetching',
        description: 'Building an application that fetches and processes API data',
        topics: ['API integration', 'Data processing', 'Error handling', 'User interface']
      },
      {
        classNumber: 24,
        title: 'Final Project: Inventory Management System',
        description: 'Complete inventory system using advanced OOP concepts',
        topics: ['System design', 'Multiple classes', 'Data persistence', 'User interface', 'Advanced features']
      },
      {
        classNumber: 25,
        title: 'Q&A / Revision',
        description: 'Interactive Q&A session and concept revision',
        topics: ['OOP concept clarification', 'Project troubleshooting', 'Best practices', 'Code optimization']
      },
      {
        classNumber: 26,
        title: 'Q&A / Revision',
        description: 'Final Q&A session and course wrap-up',
        topics: ['Advanced OOP questions', 'Project presentations', 'Next level preparation', 'Course completion']
      }
    ],
    // 🧠 PSYCHOLOGICAL TRIGGERS
    urgencyText: "⚡ ADVANCED LEVEL: Save ৳1,500!",
    socialProof: {
      studentsEnrolled: 134,
      successRate: 97,
      averageRating: 4.9
    },
    scarcityElements: {
      spotsLeft: 6,
      nextBatchDate: "February 5, 2025",
      earlyBirdDeadline: "January 5, 2025"
    },
    authoritySignals: [
      "✅ Advanced curriculum by Silicon Valley engineers",
      "🏆 Students get internships at top tech companies",
      "💻 Build production-ready applications",
      "🎯 Direct pathway to software engineering careers"
    ],
    riskReversal: {
      moneyBackGuarantee: true,
      guaranteeDays: 30,
      freeTrialClasses: 2
    },
    emotionalTriggers: {
      fearOfMissingOut: "While others learn basic coding, your teen will master ADVANCED programming that gets them hired at top companies!",
      parentWorries: [
        "😰 'My teen needs advanced skills for college applications'",
        "😟 'Other students are already building real apps'",
        "😨 'What if my child can't compete for tech internships?'"
      ],
      childDreams: [
        "💻 'I want to work at Google or Microsoft!'",
        "🚀 'I want to build the next big startup!'",
        "💰 'I want to earn six figures as a developer!'"
      ]
    },
    paymentOptions: {
      oneTime: true,
      monthly: true,
      monthlyAmount: 2400,           // Current discounted monthly price (7000/3 = 2333 → 2400)
      originalMonthlyAmount: 3000    // Original monthly price (9000/3 = 3000)
    },
    benefits: [
      '🏆 Official Advanced Certificate (college-worthy!)',
      '🎁 Professional developer toolkit',
      '💼 Production-ready GitHub portfolio',
      '👨‍💻 Personal mentor support (24/7 doubt clearing)',
      '🚀 Advanced skills that guarantee high-paying internships',
      '🎯 Lifetime access to course materials & updates',
      '👥 Exclusive alumni network of advanced programmers',
      '🏅 Priority admission to machine learning courses',
      '💡 Advanced problem-solving & system design skills',
      '🌟 Confidence to tackle any programming challenge'
    ]
  },
  {
    id: 'python-level3',
    title: 'Python Level 3 – Advanced Python',
    level: 'Advanced',
    ageGroup: 'Age 16-20',
    duration: '26 Classes (24 regular + 2 Q&A, 13 weeks, 2 per week)',
    description: 'Prepare for machine learning, algorithms, and advanced real-world projects.',
    price: 8500,
    originalPrice: 10000,
    category: 'python',
    featured: true,
    image: 'https://images.unsplash.com/photo-1517077304055-6e89abbf09b0?w=400&h=300&fit=crop',
    syllabus: [
      {
        classNumber: 1,
        title: 'Arrays & Lists (intro & usage)',
        description: 'Understanding arrays and advanced list operations',
        topics: ['Array concepts', 'List vs Array differences', 'Memory management', 'Performance considerations']
      },
      {
        classNumber: 2,
        title: 'Advanced List Operations',
        description: 'Mastering complex list manipulations and algorithms',
        topics: ['List comprehensions', 'Advanced slicing', 'List algorithms', 'Performance optimization']
      },
      {
        classNumber: 3,
        title: 'Linked Lists (concept)',
        description: 'Understanding linked list data structure concepts',
        topics: ['Linked list theory', 'Node structure', 'Advantages vs arrays', 'Use cases']
      },
      {
        classNumber: 4,
        title: 'Linked List Implementation',
        description: 'Building linked lists from scratch in Python',
        topics: ['Node class creation', 'Insertion operations', 'Deletion operations', 'Traversal methods']
      },
      {
        classNumber: 5,
        title: 'Stacks (concept & use cases)',
        description: 'Understanding stack data structure and applications',
        topics: ['LIFO principle', 'Stack operations', 'Real-world applications', 'Stack use cases']
      },
      {
        classNumber: 6,
        title: 'Stack Implementation (list-based)',
        description: 'Implementing stack using Python lists',
        topics: ['Push operation', 'Pop operation', 'Peek operation', 'Stack applications']
      },
      {
        classNumber: 7,
        title: 'Queues (concept & variations)',
        description: 'Understanding queue data structure and its types',
        topics: ['FIFO principle', 'Queue types', 'Circular queues', 'Priority queues']
      },
      {
        classNumber: 8,
        title: 'Queue Implementation',
        description: 'Building queue data structure from scratch',
        topics: ['Enqueue operation', 'Dequeue operation', 'Queue using lists', 'Queue applications']
      },
      {
        classNumber: 9,
        title: 'Quiz 1 & Review',
        description: 'Testing knowledge of basic data structures',
        topics: ['Arrays and lists quiz', 'Linked lists quiz', 'Stacks and queues review', 'Problem solving practice']
      },
      {
        classNumber: 10,
        title: 'Trees (intro & terminology)',
        description: 'Introduction to tree data structures',
        topics: ['Tree concepts', 'Tree terminology', 'Tree types', 'Tree applications']
      },
      {
        classNumber: 11,
        title: 'Binary Trees (insert & traversal)',
        description: 'Working with binary tree structures',
        topics: ['Binary tree structure', 'Node insertion', 'Tree construction', 'Basic traversal']
      },
      {
        classNumber: 12,
        title: 'Binary Tree Traversal (inorder, preorder, postorder)',
        description: 'Implementing different tree traversal methods',
        topics: ['Inorder traversal', 'Preorder traversal', 'Postorder traversal', 'Traversal applications']
      },
      {
        classNumber: 13,
        title: 'Binary Search Tree (insert, search, delete)',
        description: 'Implementing binary search tree operations',
        topics: ['BST properties', 'Insertion algorithm', 'Search algorithm', 'Deletion algorithm']
      },
      {
        classNumber: 14,
        title: 'Graph Basics (representation)',
        description: 'Introduction to graph data structures',
        topics: ['Graph concepts', 'Graph types', 'Adjacency matrix', 'Adjacency list']
      },
      {
        classNumber: 15,
        title: 'Graph Traversal (DFS)',
        description: 'Implementing Depth-First Search algorithm',
        topics: ['DFS algorithm', 'Recursive DFS', 'Iterative DFS', 'DFS applications']
      },
      {
        classNumber: 16,
        title: 'Graph Traversal (BFS)',
        description: 'Implementing Breadth-First Search algorithm',
        topics: ['BFS algorithm', 'Queue-based BFS', 'Level-order traversal', 'BFS applications']
      },
      {
        classNumber: 17,
        title: 'Quiz 2 & Review',
        description: 'Testing knowledge of advanced data structures',
        topics: ['Trees quiz', 'Graph algorithms quiz', 'Traversal methods review', 'Algorithm analysis']
      },
      {
        classNumber: 18,
        title: 'Hashing Basics & Hash Functions',
        description: 'Understanding hashing concepts and hash functions',
        topics: ['Hash function concepts', 'Hash table basics', 'Hash function design', 'Hash applications']
      },
      {
        classNumber: 19,
        title: 'Implementing a Hash Table',
        description: 'Building hash table from scratch',
        topics: ['Hash table implementation', 'Key-value storage', 'Hash function integration', 'Performance analysis']
      },
      {
        classNumber: 20,
        title: 'Collision Handling Methods',
        description: 'Dealing with hash collisions',
        topics: ['Collision types', 'Chaining method', 'Open addressing', 'Collision resolution strategies']
      },
      {
        classNumber: 21,
        title: 'Heaps (min & max heap)',
        description: 'Understanding heap data structure',
        topics: ['Heap properties', 'Min heap implementation', 'Max heap implementation', 'Heap operations']
      },
      {
        classNumber: 22,
        title: 'Priority Queues Implementation',
        description: 'Building priority queues using heaps',
        topics: ['Priority queue concept', 'Heap-based implementation', 'Priority operations', 'Real-world applications']
      },
      {
        classNumber: 23,
        title: 'Mini Project: Task Scheduler',
        description: 'Building a task scheduling system using data structures',
        topics: ['Priority-based scheduling', 'Queue management', 'Task prioritization', 'System design']
      },
      {
        classNumber: 24,
        title: 'Final Project: Mini Social Network',
        description: 'Creating a social network using graphs and advanced data structures',
        topics: ['Graph-based relationships', 'User connections', 'Data structure integration', 'System architecture']
      },
      {
        classNumber: 25,
        title: 'Q&A / Revision',
        description: 'Interactive Q&A session and concept revision',
        topics: ['Data structure review', 'Algorithm optimization', 'Performance analysis', 'Best practices']
      },
      {
        classNumber: 26,
        title: 'Q&A / Revision',
        description: 'Final Q&A session and course wrap-up',
        topics: ['Advanced algorithms', 'Project presentations', 'Career guidance', 'Course completion']
      }
    ],
    // 🧠 PSYCHOLOGICAL TRIGGERS
    urgencyText: "⚡ ADVANCED LEVEL: Save ৳1,500!",
    socialProof: {
      studentsEnrolled: 134,
      successRate: 97,
      averageRating: 4.9
    },
    scarcityElements: {
      spotsLeft: 6,
      nextBatchDate: "September 2025",
      earlyBirdDeadline: "August 2025"
    },
    authoritySignals: [
      "✅ Advanced curriculum by Silicon Valley engineers",
      "🏆 Students get internships at top tech companies",
      "💻 Build production-ready applications",
      "🎯 Direct pathway to software engineering careers"
    ],
    riskReversal: {
      moneyBackGuarantee: true,
      guaranteeDays: 30,
      freeTrialClasses: 2
    },
    emotionalTriggers: {
      fearOfMissingOut: "While others learn basic coding, your teen will master ADVANCED programming that gets them hired at top companies!",
      parentWorries: [
        "😰 'My teen needs advanced skills for college applications'",
        "😟 'Other students are already building real apps'",
        "😨 'What if my child can't compete for tech internships?'"
      ],
      childDreams: [
        "💻 'I want to work at Google or Microsoft!'",
        "🚀 'I want to build the next big startup!'",
        "💰 'I want to earn six figures as a developer!'"
      ]
    },
    paymentOptions: {
      oneTime: true,
      monthly: true,
      monthlyAmount: 2850,           // Current discounted monthly price (8500/3 = 2833 → 2850)
      originalMonthlyAmount: 3350    // Original monthly price (10000/3 = 3333 → 3350)
    },
    benefits: [
      '🏆 Official Advanced Certificate (college-worthy!)',
      '🎁 Professional developer toolkit',
      '💼 Production-ready GitHub portfolio',
      '👨‍💻 Personal mentor support (24/7 doubt clearing)',
      '🚀 Advanced skills that guarantee high-paying internships',
      '🎯 Lifetime access to course materials & updates',
      '👥 Exclusive alumni network of advanced programmers',
      '🏅 Priority admission to machine learning courses',
      '💡 Advanced problem-solving & system design skills',
      '🌟 Confidence to tackle any programming challenge'
    ]
  },
  {
    id: 'basics-robotics',
    title: 'Basics of Robotics',
    level: 'Beginner',
    ageGroup: 'Age 8-12',
    duration: '26 Classes (24 regular + 2 Q&A, 13 weeks, 2 per week)',
    description: '🤖 Build REAL robots that move, think, and respond! Watch your child\'s eyes light up as they create their first walking, talking robot friend!',
    price: 10000,
    originalPrice: 12000,
    category: 'robotics',
    featured: true,
    image: 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=400&h=300&fit=crop',
    // 🧠 PSYCHOLOGICAL TRIGGERS
    urgencyText: "🤖 ROBOT REVOLUTION: Save ৳2,000!",
    socialProof: {
      studentsEnrolled: 156,
      successRate: 94,
      averageRating: 4.8
    },
    scarcityElements: {
      spotsLeft: 8,
      nextBatchDate: "September 2025",
      earlyBirdDeadline: "August 2025"
    },
    authoritySignals: [
      "✅ Curriculum designed by MIT robotics engineers",
      "🏆 Students showcase robots at international fairs",
      "🤖 Build 5+ working robots you take home",
      "🎯 Official Arduino & Raspberry Pi certification"
    ],
    riskReversal: {
      moneyBackGuarantee: true,
      guaranteeDays: 30,
      freeTrialClasses: 2
    },
    emotionalTriggers: {
      fearOfMissingOut: "While other kids play with toys, your child will be BUILDING the toys of the future! Don't let them miss the robotics revolution!",
      parentWorries: [
        "😰 'My child only plays video games - they need hands-on skills'",
        "😟 'Robotics is the future - is my child prepared?'",
        "😨 'Other kids are building robots - mine is falling behind'"
      ],
      childDreams: [
        "🤖 'I want to build robots like in Transformers!'",
        "🚀 'I want to make robots that help people!'",
        "⚡ 'I want to be the next Tony Stark!'"
      ]
    },
    syllabus: [
      {
        classNumber: 1,
        title: 'Introduction to Arduino',
        description: 'Getting started with Arduino microcontroller and development environment',
        topics: ['Arduino IDE setup', 'Board overview', 'Basic programming structure', 'First program upload']
      },
      {
        classNumber: 2,
        title: 'Understanding Basic Electronics',
        description: 'Fundamental electronics concepts and components',
        topics: ['Voltage, current, resistance', 'Ohm\'s law', 'Electronic components', 'Circuit basics']
      },
      {
        classNumber: 3,
        title: 'Using a Multimeter',
        description: 'Learning to measure electrical properties with multimeter',
        topics: ['Voltage measurement', 'Current measurement', 'Resistance testing', 'Continuity testing']
      },
      {
        classNumber: 4,
        title: 'Working with Jumper Wires & Breadboards',
        description: 'Building circuits using breadboards and connecting components',
        topics: ['Breadboard layout', 'Jumper wire connections', 'Circuit prototyping', 'Best practices']
      },
      {
        classNumber: 5,
        title: 'Digital vs Analog Signals',
        description: 'Understanding different types of signals in electronics',
        topics: ['Digital signal characteristics', 'Analog signal properties', 'ADC and DAC concepts', 'Signal processing']
      },
      {
        classNumber: 6,
        title: 'Interfacing an LED and Buzzer',
        description: 'Working with basic output components',
        topics: ['LED circuit design', 'Current limiting resistors', 'Buzzer control', 'PWM for brightness/volume']
      },
      {
        classNumber: 7,
        title: 'Using the 7-Segment Display',
        description: 'Working with 7-segment displays for numeric output',
        topics: ['7-segment wiring', 'BCD to 7-segment conversion', 'Multiplexing displays', 'Counter projects']
      },
      {
        classNumber: 8,
        title: 'Interfacing the I2C LCD Display',
        description: 'Using LCD displays with I2C communication',
        topics: ['I2C protocol basics', 'LCD library usage', 'Text display programming', 'Custom characters']
      },
      {
        classNumber: 9,
        title: 'Ultrasonic Sensor (HC-SR04) for Distance Measurement',
        description: 'Measuring distances using ultrasonic sensors',
        topics: ['HC-SR04 wiring', 'Distance calculation', 'Calibration techniques', 'Range finder projects']
      },
      {
        classNumber: 10,
        title: 'IR Sensors for Object Detection',
        description: 'Using infrared sensors for object detection',
        topics: ['IR sensor types', 'Digital vs analog IR', 'Object detection logic', 'Proximity sensing']
      },
      {
        classNumber: 11,
        title: 'Joystick Module for Motion Control',
        description: 'Implementing joystick-based control systems',
        topics: ['Analog joystick reading', 'X-Y coordinate mapping', 'Button integration', 'Control algorithms']
      },
      {
        classNumber: 12,
        title: 'Servo Motor Control',
        description: 'Controlling servo motors for precise positioning',
        topics: ['Servo motor principles', 'PWM control signals', 'Position control', 'Multi-servo coordination']
      },
      {
        classNumber: 13,
        title: 'Relay Module for High-Voltage Control',
        description: 'Controlling high-voltage devices safely using relays',
        topics: ['Relay operation principles', 'Relay driver circuits', 'Safety considerations', 'AC device control']
      },
      {
        classNumber: 14,
        title: 'RFID Module for Access Control',
        description: 'Implementing RFID-based access control systems',
        topics: ['RFID technology basics', 'Card/tag reading', 'Access control logic', 'Security applications']
      },
      {
        classNumber: 15,
        title: 'Bluetooth Communication (HC-06)',
        description: 'Wireless communication using Bluetooth modules',
        topics: ['Bluetooth module setup', 'Serial communication', 'Mobile app control', 'Data transmission']
      },
      {
        classNumber: 16,
        title: 'DHT11 Temperature & Humidity Sensor',
        description: 'Environmental monitoring with DHT11 sensor',
        topics: ['DHT11 sensor interfacing', 'Temperature reading', 'Humidity measurement', 'Data logging']
      },
      {
        classNumber: 17,
        title: 'Soil Moisture Sensor for Smart Irrigation',
        description: 'Building smart irrigation systems',
        topics: ['Soil moisture detection', 'Threshold-based control', 'Automatic watering', 'Plant care systems']
      },
      {
        classNumber: 18,
        title: 'LDR (Light Dependent Resistor) for Light Sensing',
        description: 'Light-based control systems using LDR',
        topics: ['LDR characteristics', 'Light level detection', 'Automatic lighting', 'Day/night sensing']
      },
      {
        classNumber: 19,
        title: 'Gas Detection using MQ-2 Sensor',
        description: 'Building gas detection and safety systems',
        topics: ['MQ-2 sensor operation', 'Gas concentration measurement', 'Alarm systems', 'Safety applications']
      },
      {
        classNumber: 20,
        title: 'Water Level Detection using Water Sensor',
        description: 'Implementing water level monitoring systems',
        topics: ['Water sensor types', 'Level detection logic', 'Overflow protection', 'Tank monitoring']
      },
      {
        classNumber: 21,
        title: 'Interfacing an IMU (Inertial Measurement Unit)',
        description: 'Working with motion and orientation sensors',
        topics: ['IMU sensor basics', 'Accelerometer data', 'Gyroscope readings', 'Motion detection']
      },
      {
        classNumber: 22,
        title: 'Implementing a Robotic Chassis (2-Wheel Car)',
        description: 'Building the foundation for mobile robots',
        topics: ['Chassis assembly', 'Motor mounting', 'Wheel alignment', 'Basic movement control']
      },
      {
        classNumber: 23,
        title: 'Obstacle Avoidance Robot',
        description: 'Creating an autonomous obstacle-avoiding robot',
        topics: ['Sensor integration', 'Avoidance algorithms', 'Path planning', 'Real-time control']
      },
      {
        classNumber: 24,
        title: 'Bluetooth-Controlled Robot',
        description: 'Building a smartphone-controlled robot',
        topics: ['Bluetooth integration', 'Mobile app control', 'Remote operation', 'Command processing']
      },
      {
        classNumber: 25,
        title: 'Automated Door Lock System (RFID + Relay)',
        description: 'Building a complete access control system',
        topics: ['RFID authentication', 'Relay-controlled locks', 'Security protocols', 'System integration']
      },
      {
        classNumber: 26,
        title: 'Weather Monitoring System',
        description: 'Creating a comprehensive weather station',
        topics: ['Multi-sensor integration', 'Data collection', 'Display systems', 'IoT connectivity']
      }
    ],
    paymentOptions: {
      oneTime: true,
      monthly: true,
      monthlyAmount: 3400,           // Current discounted monthly price (10000/3 = 3333 → 3400)
      originalMonthlyAmount: 4000    // Original monthly price (12000/3 = 4000)
    },
    benefits: [
      'Certificate of completion',
      'Special gifts upon course completion',
      'Hands-on robotics projects',
      'Arduino programming skills',
      'Expert instructor guidance'
    ]
  },
  {
    id: 'ml-algorithms',
    title: 'Machine Learning & Algorithms',
    level: 'Intermediate',
    ageGroup: 'Age 15-20',
    duration: '26 Classes (24 regular + 2 Q&A, 13 weeks, 2 per week)',
    description: 'Learn core machine learning concepts and algorithms with Python for real-world applications.',
    price: 11000,
    originalPrice: 15000,
    category: 'ml',
    featured: true,
    image: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=400&h=300&fit=crop',
    syllabus: [
      {
        classNumber: 1,
        title: 'What is AI & ML? Real-life examples',
        description: 'Introduction to artificial intelligence and machine learning',
        topics: ['AI vs ML', 'Real-world applications', 'Future of AI']
      },
      {
        classNumber: 2,
        title: 'AI vs Machine Learning',
        description: 'Understanding the differences and relationships',
        topics: ['AI definitions', 'ML types', 'Deep learning basics']
      },
      {
        classNumber: 3,
        title: 'Python refresher for ML',
        description: 'Essential Python concepts for machine learning',
        topics: ['Python basics', 'Data types', 'Control structures']
      },
      {
        classNumber: 4,
        title: 'Advanced Python for ML',
        description: 'Python libraries and tools for data science',
        topics: ['Object-oriented Python', 'Error handling', 'File operations']
      },
      {
        classNumber: 5,
        title: 'NumPy basics',
        description: 'Introduction to numerical computing',
        topics: ['NumPy arrays', 'Array operations', 'Mathematical functions']
      },
      {
        classNumber: 6,
        title: 'NumPy & Pandas basics',
        description: 'Working with numerical and data analysis libraries',
        topics: ['Pandas DataFrames', 'Data manipulation', 'Data selection']
      },
      {
        classNumber: 7,
        title: 'Advanced Pandas',
        description: 'Advanced data manipulation techniques',
        topics: ['Groupby operations', 'Data aggregation', 'Time series data']
      },
      {
        classNumber: 8,
        title: 'Data cleaning basics',
        description: 'Preparing data for analysis',
        topics: ['Missing data', 'Data types', 'Data validation']
      },
      {
        classNumber: 9,
        title: 'Data cleaning & visualization',
        description: 'Preparing and visualizing data for analysis',
        topics: ['Data cleaning', 'Matplotlib', 'Seaborn visualization']
      },
      {
        classNumber: 10,
        title: 'Advanced Data Visualization',
        description: 'Creating compelling data visualizations',
        topics: ['Chart customization', 'Interactive plots', 'Dashboard creation']
      },
      {
        classNumber: 11,
        title: 'Supervised learning basics',
        description: 'Introduction to supervised learning',
        topics: ['Training data', 'Labels', 'Model types']
      },
      {
        classNumber: 12,
        title: 'Supervised learning (Linear Regression)',
        description: 'Understanding supervised learning with linear regression',
        topics: ['Linear regression', 'Model training', 'Prediction']
      },
      {
        classNumber: 13,
        title: 'Advanced Linear Regression',
        description: 'Building robust regression models',
        topics: ['Feature engineering', 'Model evaluation', 'Regularization']
      },
      {
        classNumber: 14,
        title: 'Classification basics (KNN)',
        description: 'Introduction to classification algorithms',
        topics: ['K-Nearest Neighbors', 'Classification metrics', 'Model evaluation']
      },
      {
        classNumber: 15,
        title: 'Advanced Classification',
        description: 'Building classification systems',
        topics: ['Feature scaling', 'Cross-validation', 'Hyperparameter tuning']
      },
      {
        classNumber: 16,
        title: 'Train/Test split',
        description: 'Proper data splitting for model validation',
        topics: ['Data splitting', 'Cross-validation', 'Overfitting prevention']
      },
      {
        classNumber: 17,
        title: 'Model Validation Techniques',
        description: 'Ensuring model reliability',
        topics: ['K-fold cross-validation', 'Stratified sampling', 'Validation metrics']
      },
      {
        classNumber: 18,
        title: 'Intro to Neural Networks',
        description: 'Basics of artificial neural networks',
        topics: ['Neural network architecture', 'Activation functions', 'Training process']
      },
      {
        classNumber: 19,
        title: 'Neural Network Implementation',
        description: 'Building neural networks from scratch',
        topics: ['Forward propagation', 'Backpropagation', 'Gradient descent']
      },
      {
        classNumber: 20,
        title: 'Algorithms: Sorting & Searching',
        description: 'Fundamental algorithms for data processing',
        topics: ['Bubble sort', 'Quick sort', 'Binary search', 'Linear search']
      },
      {
        classNumber: 21,
        title: 'Algorithm Complexity',
        description: 'Understanding algorithm efficiency',
        topics: ['Big O notation', 'Time complexity', 'Space complexity']
      },
      {
        classNumber: 22,
        title: 'Recursion & problem-solving',
        description: 'Solving complex problems using recursion',
        topics: ['Recursive thinking', 'Base cases', 'Problem decomposition']
      },
      {
        classNumber: 23,
        title: 'Model deployment basics',
        description: 'Deploying machine learning models',
        topics: ['Model saving', 'API creation', 'Web deployment']
      },
      {
        classNumber: 24,
        title: 'Final Project: ML-powered Application',
        description: 'Complete machine learning application',
        topics: ['End-to-end ML pipeline', 'Model optimization', 'Real-world deployment']
      },
      {
        classNumber: 25,
        title: 'Q&A Session 1',
        description: 'Interactive Q&A session to clarify doubts and review ML concepts',
        topics: ['Algorithm review', 'Model optimization', 'Best practices']
      },
      {
        classNumber: 26,
        title: 'Q&A Session 2',
        description: 'Final Q&A session and course wrap-up',
        topics: ['Advanced questions', 'Next steps', 'Course completion']
      }
    ],
    // 🧠 PSYCHOLOGICAL TRIGGERS
    urgencyText: "🤖 AI REVOLUTION: Save ৳4,000!",
    socialProof: {
      studentsEnrolled: 98,
      successRate: 95,
      averageRating: 4.8
    },
    scarcityElements: {
      spotsLeft: 4,
      nextBatchDate: "September 2025",
      earlyBirdDeadline: "August 2025"
    },
    authoritySignals: [
      "✅ Curriculum designed by Google AI researchers",
      "🏆 Students land ML internships at top companies",
      "🤖 Build AI models used in real applications",
      "🎯 Direct pathway to AI/ML engineering careers"
    ],
    riskReversal: {
      moneyBackGuarantee: true,
      guaranteeDays: 30,
      freeTrialClasses: 2
    },
    emotionalTriggers: {
      fearOfMissingOut: "While others struggle with basic programming, your teen will master ARTIFICIAL INTELLIGENCE - the technology that's reshaping the world!",
      parentWorries: [
        "😰 'AI is the future - is my child prepared?'",
        "😟 'Other students are already building AI models'",
        "😨 'What if my child misses the AI revolution?'"
      ],
      childDreams: [
        "🤖 'I want to build AI like ChatGPT!'",
        "🚀 'I want to work on self-driving cars!'",
        "💰 'I want to be an AI engineer at Tesla!'"
      ]
    },
    paymentOptions: {
      oneTime: true,
      monthly: true,
      monthlyAmount: 3700,           // Current discounted monthly price (11000/3 = 3666 → 3700)
      originalMonthlyAmount: 5000    // Original monthly price (15000/3 = 5000)
    },
    benefits: [
      '🏆 Official ML Certificate (industry-recognized!)',
      '🎁 Professional AI toolkit',
      '💼 Production-ready ML portfolio on GitHub',
      '👨‍💻 Personal AI mentor support (24/7 doubt clearing)',
      '🚀 Future-proof AI skills that guarantee high-paying careers',
      '🎯 Lifetime access to course materials & updates',
      '👥 Exclusive alumni network of AI engineers',
      '🏅 Priority admission to advanced AI courses',
      '💡 Advanced AI thinking & problem-solving skills',
      '🌟 Confidence to build the next generation of AI'
    ]
  }
];

export const projects: Project[] = [
  {
    id: 'line-follower',
    title: 'Smart Line Follower Robot',
    description: 'An autonomous robot that follows lines using advanced sensors and PID control algorithms. Built by robotics students using Arduino and multiple sensors.',
    image: 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=400&h=300&fit=crop',
    category: 'robotics',
    studentName: 'Aarav Sharma',
    featured: true
  },
  {
    id: 'python-quiz-game',
    title: 'Interactive Quiz Game',
    description: 'A comprehensive quiz application built with Python, featuring multiple categories, scoring system, and user-friendly interface.',
    image: 'https://images.unsplash.com/photo-1517077304055-6e89abbf09b0?w=400&h=300&fit=crop',
    category: 'python',
    studentName: 'Priya Patel',
    featured: true
  },
  {
    id: 'weather-app',
    title: 'Weather Forecast App',
    description: 'A weather application that fetches real-time data from APIs and displays forecasts with beautiful visualizations using Python and Flask.',
    image: 'https://images.unsplash.com/photo-1592210454359-9043f067919b?w=400&h=300&fit=crop',
    category: 'python',
    studentName: 'Rahul Kumar',
    featured: false
  },
  {
    id: 'gesture-robot',
    title: 'Gesture Controlled Robot',
    description: 'A robot that responds to hand gestures using computer vision and machine learning algorithms for intuitive control.',
    image: 'https://images.unsplash.com/photo-1517077304055-6e89abbf09b0?w=400&h=300&fit=crop',
    category: 'robotics',
    studentName: 'Zara Ahmed',
    featured: true
  },
  {
    id: 'ml-chatbot',
    title: 'AI Chatbot Assistant',
    description: 'An intelligent chatbot built with machine learning that can answer questions and assist users with various tasks.',
    image: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=400&h=300&fit=crop',
    category: 'ml',
    studentName: 'Arjun Singh',
    featured: true
  },
  {
    id: 'smart-traffic-light',
    title: 'Smart Traffic Light System',
    description: 'An intelligent traffic management system that adapts to traffic flow using sensors and machine learning algorithms.',
    image: 'https://images.unsplash.com/photo-1545459720-aac8509eb02c?w=400&h=300&fit=crop',
    category: 'robotics',
    studentName: 'Ananya Desai',
    featured: false
  },
  {
    id: 'data-visualization',
    title: 'Data Visualization Dashboard',
    description: 'A comprehensive dashboard for analyzing and visualizing complex datasets using Python, Pandas, and Matplotlib.',
    image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop',
    category: 'ml',
    studentName: 'Vikram Malhotra',
    featured: false
  },
  {
    id: 'voice-controlled-bot',
    title: 'Voice Controlled Robot',
    description: 'A robot that responds to voice commands using speech recognition technology and natural language processing.',
    image: 'https://images.unsplash.com/photo-1517077304055-6e89abbf09b0?w=400&h=300&fit=crop',
    category: 'robotics',
    studentName: 'Ishaan Reddy',
    featured: false
  }
];

export const testimonials: Testimonial[] = [
  {
    id: '1',
    name: 'Aarav Sharma',
    age: 12,
    course: 'Python Level 1 – Coding for Kids',
    content: 'I never thought coding could be this fun! The teachers make everything so easy to understand. I built my own quiz game and it was amazing!',
    rating: 5,
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face'
  },
  {
    id: '2',
    name: 'Priya Patel',
    age: 14,
    course: 'Python Level 2 – Intermediate Python',
    content: 'The intermediate course really helped me understand how to build real applications. I created a calculator and a digital clock - so cool!',
    rating: 5,
    image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face'
  },
  {
    id: '3',
    name: 'Rahul Kumar',
    age: 17,
    course: 'Python Level 3 – Advanced Python',
    content: 'Advanced Python course was incredible! I learned web development with Flask and built a weather app. Now I feel ready for college computer science.',
    rating: 5,
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face'
  },
  {
    id: '4',
    name: 'Zara Ahmed',
    age: 11,
    course: 'Robotics Level 1 – Beginner Robotics',
    content: 'Building robots is the coolest thing ever! I made a line follower car and it actually works. The teachers explain everything step by step.',
    rating: 5,
    image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face'
  },
  {
    id: '5',
    name: 'Arjun Singh',
    age: 18,
    course: 'Machine Learning & Algorithms',
    content: 'The ML course opened my eyes to the future of technology. I built an AI chatbot and learned real algorithms. This is exactly what I wanted to learn!',
    rating: 5,
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face'
  },
  {
    id: '6',
    name: 'Ananya Desai',
    age: 15,
    course: 'Robotics Level 2 – Advanced Robotics',
    content: 'Advanced robotics is mind-blowing! I integrated Python with Arduino and built a gesture-controlled robot. The possibilities are endless!',
    rating: 5,
    image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face'
  }
];

export const pricingPlans = [
  {
    name: 'Basic Plan',
    price: 299,
    duration: 'per course',
    features: [
      '24 classes (2 per week)',
      'Hands-on projects',
      'Course materials',
      'Certificate of completion',
      'Email support'
    ],
    popular: false
  },
  {
    name: 'Premium Plan',
    price: 799,
    duration: '3 courses bundle',
    features: [
      'All Basic features',
      'Priority support',
      '1-on-1 mentoring sessions',
      'Advanced project guidance',
      'Portfolio building support'
    ],
    popular: true
  },
  {
    name: 'Complete Package',
    price: 1299,
    duration: 'all courses',
    features: [
      'All Premium features',
      'Lifetime access to materials',
      'Career counseling',
      'Internship opportunities',
      'Exclusive workshops'
    ],
    popular: false
  }
];
