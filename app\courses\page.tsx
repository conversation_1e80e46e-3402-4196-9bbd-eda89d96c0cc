'use client';

import { useState } from 'react';
import Link from 'next/link';
import { courses } from '@/lib/data';
import DemoClassForm from '@/components/DemoClassForm';
import EnrollmentForm from '@/components/EnrollmentForm';

export default function CoursesPage() {
  const [isDemoFormOpen, setIsDemoFormOpen] = useState(false);
  const [isEnrollmentFormOpen, setIsEnrollmentFormOpen] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState(null);

  const handleEnrollClick = (course: any) => {
    setSelectedCourse(course);
    setIsEnrollmentFormOpen(true);
  };
  // Filter and organize courses to show all 5 courses
  const displayCourses = courses.map(course => ({
    id: course.id,
    title: course.title,
    description: course.description,
    age: course.ageGroup,
    duration: course.duration,
    sessions: course.duration,
    price: course.price,
    originalPrice: course.originalPrice,
    savings: course.originalPrice - course.price,
    monthlyAmount: course.paymentOptions.monthlyAmount,
    level: course.level,
    category: course.category,
    benefits: course.benefits,
    topics: course.syllabus.slice(0, 4).map(item => item.title),
    href: `/courses/${course.id}`
  }));

  const features = [
    {
      title: 'Special Discount Pricing',
      description: 'Save up to ৳4,000 on course fees with our limited-time offer'
    },
    {
      title: 'Flexible Payment Options',
      description: 'Choose one-time payment or convenient monthly installments'
    },
    {
      title: 'Certificate & Gifts',
      description: 'Receive completion certificate and special gifts upon finishing'
    },
    {
      title: 'Expert Instruction',
      description: 'Learn from Polok Poddar, AI researcher and robotics educator'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center mb-12">

            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Programming Courses
            </h1>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-4">
              Structured learning paths designed for different age groups and skill levels.
              Master programming through hands-on projects and expert guidance.
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4 text-sm">
              <div className="flex items-center text-green-600">
                <span className="font-medium">✓ Certificate & Gifts Included</span>
              </div>
              <div className="flex items-center text-blue-600">
                <span className="font-medium">✓ Monthly Payment Options</span>
              </div>
              <div className="flex items-center text-purple-600">
                <span className="font-medium">✓ Expert Instructor</span>
              </div>
            </div>
          </div>

          {/* Features Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            {features.map((feature, index) => (
              <div key={index} className="text-center">
                <h3 className="font-semibold text-gray-900 mb-2">{feature.title}</h3>
                <p className="text-sm text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>


        </div>
      </div>

      {/* Demo Class CTA */}
      <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border-t border-b border-yellow-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            🌟 Try Before You Enroll!
          </h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Join our FREE demo class every Friday at 4:00 PM. Experience our interactive teaching style
            and see if our courses are right for your child. Minimum 5 students required.
          </p>
          <button
            onClick={() => setIsDemoFormOpen(true)}
            className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
          >
            ⭐ Register for FREE Demo Class
          </button>
        </div>
      </div>

      {/* Courses Grid */}
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <h2 className="text-2xl font-bold text-gray-900 mb-8">Available Courses</h2>

        <div className="grid lg:grid-cols-3 gap-8">
          {displayCourses.map((course) => (
            <div key={course.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                    course.level === 'Beginner' ? 'bg-green-100 text-green-800' :
                    course.level === 'Intermediate' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {course.level}
                  </span>
                  <div className="text-right">
                    <div className="flex items-center gap-2">
                      <span className="text-lg font-bold text-green-600">৳{course.price.toLocaleString()}</span>
                      <span className="text-sm text-gray-500 line-through">৳{course.originalPrice.toLocaleString()}</span>
                    </div>
                    <div className="text-xs text-green-600 font-medium">Save ৳{course.savings.toLocaleString()}</div>
                  </div>
                </div>

                <h3 className="text-xl font-bold text-gray-900 mb-2">{course.title}</h3>
                <p className="text-gray-600 mb-4">{course.description}</p>

                <div className="space-y-2 mb-6">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Age Group:</span>
                    <span className="font-medium">{course.age}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Duration:</span>
                    <span className="font-medium">{course.duration}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Monthly Option:</span>
                    <div className="text-right">
                      <div className="font-medium text-blue-600">৳{course.monthlyAmount?.toLocaleString()}/month</div>
                      <div className="text-xs text-gray-500 line-through">৳3,000/month</div>
                      <div className="text-xs text-green-600 font-semibold">Save ৳600/month!</div>
                    </div>
                  </div>
                </div>

                <div className="mb-6">
                  <h4 className="font-medium text-gray-900 mb-2">Key Topics:</h4>
                  <div className="flex flex-wrap gap-2">
                    {course.topics.map((topic, index) => (
                      <span key={index} className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                        {topic}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Benefits */}
                <div className="mb-6 p-3 bg-green-50 rounded-lg border border-green-200">
                  <div className="text-xs font-medium text-green-800 mb-2">What You Get:</div>
                  <div className="space-y-1">
                    {course.benefits.slice(0, 2).map((benefit, index) => (
                      <div key={index} className="flex items-start">
                        <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-1.5 mr-2 flex-shrink-0"></div>
                        <span className="text-xs text-green-700">{benefit}</span>
                      </div>
                    ))}
                    {course.benefits.length > 2 && (
                      <div className="text-xs text-green-600 font-medium">
                        +{course.benefits.length - 2} more benefits
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-3">
                  {/* Enroll Button */}
                  <button
                    onClick={() => handleEnrollClick(course)}
                    className="w-full bg-gradient-to-r from-green-600 to-emerald-600 text-white py-3 px-4 rounded-lg font-semibold hover:from-green-700 hover:to-emerald-700 transition-all duration-300 transform hover:scale-105 shadow-lg"
                  >
                    Enroll Now
                  </button>

                  <Link href={course.href} className="block w-full text-center btn-secondary">
                    View Details
                  </Link>

                  <div className="text-center">
                    <span className="text-xs text-gray-500">
                      Or pay ৳{course.monthlyAmount?.toLocaleString()}/month • BDT (Bangladeshi Taka)
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Demo Class Form */}
      <DemoClassForm
        isOpen={isDemoFormOpen}
        onClose={() => setIsDemoFormOpen(false)}
      />

      {/* Enrollment Form */}
      <EnrollmentForm
        isOpen={isEnrollmentFormOpen}
        onClose={() => setIsEnrollmentFormOpen(false)}
        selectedCourse={selectedCourse}
      />
    </div>
  );
}

