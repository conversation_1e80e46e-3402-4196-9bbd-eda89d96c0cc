@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --primary-50: #f0f9ff;
  --primary-100: #e0f2fe;
  --primary-200: #bae6fd;
  --primary-300: #7dd3fc;
  --primary-400: #38bdf8;
  --primary-500: #0ea5e9;
  --primary-600: #0284c7;
  --primary-700: #0369a1;
  --primary-800: #075985;
  --primary-900: #0c4a6e;

  --secondary-50: #f8fafc;
  --secondary-100: #f1f5f9;
  --secondary-200: #e2e8f0;
  --secondary-300: #cbd5e1;
  --secondary-400: #94a3b8;
  --secondary-500: #64748b;
  --secondary-600: #475569;
  --secondary-700: #334155;
  --secondary-800: #1e293b;
  --secondary-900: #0f172a;

  --accent-50: #ecfdf5;
  --accent-100: #d1fae5;
  --accent-200: #a7f3d0;
  --accent-500: #10b981;
  --accent-600: #059669;
}

@layer base {
  html {
    scroll-behavior: smooth;
    font-family: 'Inter', sans-serif;
    overflow-x: hidden;
  }

  body {
    @apply text-gray-700 bg-gray-50;
    transition: all 0.3s ease;
    overflow-x: hidden;
    min-height: 100vh;
  }

  * {
    transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease;
    box-sizing: border-box;
  }

  /* Enhanced smooth scrolling for all browsers */
  html, body {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  /* Ensure all elements are responsive */
  img, video, iframe {
    max-width: 100%;
    height: auto;
  }
}

@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-3 px-8 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl;
  }

  .btn-secondary {
    @apply bg-white hover:bg-gray-50 text-gray-700 font-medium py-3 px-6 rounded-lg transition-all duration-300 border border-gray-300 hover:border-blue-300 hover:shadow-md;
  }

  .card {
    @apply bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 border border-gray-100 hover:border-blue-200;
  }

  .section-padding {
    @apply py-16 px-4 sm:px-6 lg:px-8;
  }

  .container-custom {
    @apply max-w-6xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Responsive container variations */
  .container-fluid {
    @apply w-full max-w-none px-4 sm:px-6 lg:px-8;
  }

  .container-tight {
    @apply max-w-4xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-blue-600 via-blue-700 to-emerald-600 bg-clip-text text-transparent;
  }

  .bg-gradient-hero {
    @apply bg-gradient-to-br from-blue-50 via-white to-emerald-50;
  }
}

@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.8s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.6s ease-out;
  }

  .animate-bounce-gentle {
    animation: bounceGentle 2s ease-in-out infinite;
  }

  .animate-scale-in {
    animation: scaleIn 0.5s ease-out;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.6s ease-out;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.6s ease-out;
  }

  .hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }

  .smooth-scroll {
    scroll-behavior: smooth;
  }

  .glass-effect {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Enhanced responsive utilities */
  .responsive-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8;
  }

  .responsive-flex {
    @apply flex flex-col sm:flex-row gap-4 sm:gap-6;
  }

  .responsive-text {
    @apply text-sm sm:text-base lg:text-lg;
  }

  .responsive-heading {
    @apply text-2xl sm:text-3xl lg:text-4xl xl:text-5xl;
  }

  .responsive-padding {
    @apply p-4 sm:p-6 lg:p-8;
  }

  .responsive-margin {
    @apply m-4 sm:m-6 lg:m-8;
  }

  /* Smooth scroll for anchor links */
  .smooth-anchor {
    scroll-margin-top: 80px; /* Account for fixed navbar */
  }

  /* Fix navbar overlap issues */
  .navbar-offset {
    padding-top: 80px; /* Account for sticky navbar height */
  }

  .section-with-navbar {
    scroll-margin-top: 80px;
  }

  /* Prevent horizontal overflow */
  .no-overflow {
    overflow-x: hidden;
    max-width: 100vw;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceGentle {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.navbar-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 3s ease-in-out infinite;
}
