'use client';

import { motion } from 'framer-motion';
import { Users, Target, BookOpen, Code, Star } from 'lucide-react';
import WorkshopGallery from '../../components/WorkshopGallery';

export default function AboutPage() {
  const values = [
    {
      icon: Code,
      title: 'Innovation',
      description: 'We believe in pushing boundaries and exploring new technologies.'
    },
    {
      icon: Users,
      title: 'Community',
      description: 'Building a supportive network of learners and creators.'
    },
    {
      icon: Target,
      title: 'Excellence',
      description: 'Striving for the highest quality in education and outcomes.'
    },
    {
      icon: BookOpen,
      title: 'Learning',
      description: 'Continuous improvement and knowledge sharing.'
    }
  ];

  const stats = [
    { number: '100+', label: 'Happy Students' },
    { number: '95%', label: 'Success Rate' },
    { number: '5', label: 'Specialized Courses' },
    { number: '24', label: 'Classes per Course' }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-secondary-900 mb-4">
              About DOTPY Academy
            </h1>
            <p className="text-lg text-secondary-600 max-w-3xl mx-auto">
              We are passionate about empowering the next generation with cutting-edge technology education. 
              Our mission is to make coding, robotics, and machine learning accessible to everyone.
            </p>
          </div>
        </div>
      </div>

      {/* Mission Statement */}
      <section className="section-padding">
        <div className="container-custom">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
            >
              <h2 className="text-3xl md:text-4xl font-bold text-secondary-900 mb-6">
                Our Mission
              </h2>
              <p className="text-lg text-secondary-600 mb-6">
                At DOTPY Academy, we believe that every child deserves the opportunity to learn and excel in technology. 
                Our mission is to provide high-quality, hands-on education in Python programming, robotics, and machine learning.
              </p>
              <p className="text-lg text-secondary-600 mb-6">
                We focus on practical learning through real-world projects, ensuring that students not only understand 
                theoretical concepts but can also apply them to solve actual problems.
              </p>
              <p className="text-lg text-secondary-600">
                Our goal is to inspire the next generation of innovators, problem-solvers, and technology leaders.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="relative"
            >
              <div className="bg-gradient-to-br from-primary-50 to-accent-50 rounded-2xl p-8">
                <div className="text-center">
                  <div className="text-6xl mb-4">🎯</div>
                  <h3 className="text-xl font-semibold text-secondary-900 mb-4">
                    What We Believe
                  </h3>
                  <ul className="text-left space-y-3 text-secondary-700">
                    <li className="flex items-start">
                      <Star className="h-5 w-5 text-primary-600 mr-3 mt-0.5 flex-shrink-0" />
                      <span>Every student has unique potential</span>
                    </li>
                    <li className="flex items-start">
                      <Star className="h-5 w-5 text-primary-600 mr-3 mt-0.5 flex-shrink-0" />
                      <span>Learning should be fun and engaging</span>
                    </li>
                    <li className="flex items-start">
                      <Star className="h-5 w-5 text-primary-600 mr-3 mt-0.5 flex-shrink-0" />
                      <span>Practical skills matter more than theory</span>
                    </li>
                    <li className="flex items-start">
                      <Star className="h-5 w-5 text-primary-600 mr-3 mt-0.5 flex-shrink-0" />
                      <span>Innovation starts with education</span>
                    </li>
                  </ul>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Why Choose DOTPY Academy */}
      <section className="section-padding bg-white">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-secondary-900 mb-4">
              Why Choose DOTPY Academy?
            </h2>
            <p className="text-lg text-secondary-600 max-w-3xl mx-auto">
              We provide a unique learning experience that combines theoretical knowledge with practical application, 
              ensuring you develop real-world skills that matter.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center group"
              >
                <div className="bg-primary-50 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary-100 transition-colors duration-300">
                  <value.icon className="h-8 w-8 text-primary-600" />
                </div>
                <h3 className="text-xl font-semibold text-secondary-900 mb-3">
                  {value.title}
                </h3>
                <p className="text-secondary-600 leading-relaxed">
                  {value.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Founder & Instructor Section */}
      <section className="section-padding">
        <div className="container-custom">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="order-2 lg:order-1"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-secondary-900 mb-6">
                Meet Our Founder & Lead Instructor
              </h2>
              <h3 className="text-2xl font-semibold text-primary-600 mb-2">
                Polok Poddar
              </h3>
              <p className="text-lg font-medium text-gray-700 mb-4">
                AI Researcher, Embedded & IoT Developer, and Robotics Educator
              </p>
              <p className="text-lg text-secondary-600 mb-6">
                Passionate about building intelligent solutions and inspiring the next generation of tech leaders.
                Polok is a BRAC University graduate who combines deep technical expertise with a genuine love for teaching.
              </p>

              <div className="space-y-4 mb-6">
                <div className="flex items-start">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span className="text-gray-700">AI Researcher with expertise in machine learning and neural networks</span>
                </div>
                <div className="flex items-start">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span className="text-gray-700">Embedded & IoT Developer specializing in smart systems</span>
                </div>
                <div className="flex items-start">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span className="text-gray-700">Robotics Educator with hands-on project experience</span>
                </div>
                <div className="flex items-start">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span className="text-gray-700">BRAC University graduate with strong academic foundation</span>
                </div>
              </div>

              <div className="flex items-center space-x-6 mb-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">100+</div>
                  <div className="text-sm text-gray-500">Happy Students</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">5</div>
                  <div className="text-sm text-gray-500">Specialized Courses</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">95%</div>
                  <div className="text-sm text-gray-500">Success Rate</div>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <a
                  href="https://iamproloy.vercel.app/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="btn-primary inline-flex items-center"
                >
                  View Portfolio
                  <svg className="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </a>
                <a href="/contact" className="btn-secondary">
                  Get in Touch
                </a>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="order-1 lg:order-2"
            >
              <div className="relative">
                <div className="bg-gradient-to-br from-blue-50 to-emerald-50 rounded-2xl p-8">
                  <img
                    src="/founder.jpg"
                    alt="Polok Poddar - Founder & Lead Instructor of dotPY Academy"
                    className="w-full h-96 object-cover rounded-xl shadow-lg"
                    onError={(e) => {
                      // Fallback to a placeholder if image doesn't load
                      e.currentTarget.src = "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=500&h=600&fit=crop&crop=face";
                    }}
                  />
                </div>

                {/* Floating badges */}
                <div className="absolute -bottom-4 -right-4 bg-white p-3 rounded-xl shadow-lg border border-gray-100">
                  <div className="text-center">
                    <div className="text-xl mb-1">🎓</div>
                    <div className="text-xs font-medium text-gray-900">BRAC University</div>
                  </div>
                </div>
                <div className="absolute -top-4 -left-4 bg-white p-3 rounded-xl shadow-lg border border-gray-100">
                  <div className="text-center">
                    <div className="text-xl mb-1">🤖</div>
                    <div className="text-xs font-medium text-gray-900">AI Researcher</div>
                  </div>
                </div>
                <div className="absolute top-1/2 -right-6 bg-white p-3 rounded-xl shadow-lg border border-gray-100">
                  <div className="text-center">
                    <div className="text-xl mb-1">⚡</div>
                    <div className="text-xs font-medium text-gray-900">IoT Expert</div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="section-padding bg-primary-600">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Our Impact in Numbers
            </h2>
            <p className="text-lg text-primary-100 max-w-2xl mx-auto">
              See how we've been making a difference in technology education.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="text-4xl font-bold text-white mb-2">
                  {stat.number}
                </div>
                <div className="text-primary-100">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="section-padding bg-white">
        <div className="container-custom text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-secondary-900 mb-4">
              Ready to Start Your Journey?
            </h2>
            <p className="text-lg text-secondary-600 mb-8 max-w-2xl mx-auto">
              Join hundreds of students who are already learning and creating amazing projects with DOTPY Academy.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a href="/courses" className="btn-primary">
                Explore Courses
              </a>
              <a href="/contact" className="btn-secondary">
                Get in Touch
              </a>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Workshop Gallery */}
      <WorkshopGallery />
    </div>
  );
}
