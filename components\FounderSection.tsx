'use client';

import { motion } from 'framer-motion';
import { ExternalLink, GraduationCap, Brain, Cpu } from 'lucide-react';

const FounderSection = () => {
  return (
    <section className="py-16 bg-gradient-to-br from-blue-50 via-white to-emerald-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Founder Image */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="relative"
          >
            <div className="relative">
              <div className="bg-gradient-to-br from-blue-100 to-emerald-100 rounded-2xl p-6">
                <img
                  src="/founder.jpg"
                  alt="<PERSON><PERSON> - Founder of dotPY Academy"
                  className="w-full h-80 object-cover rounded-xl shadow-lg"
                  onError={(e) => {
                    e.currentTarget.src = "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=500&h=600&fit=crop&crop=face";
                  }}
                />
              </div>
              
              {/* Floating badges */}
              <motion.div
                animate={{ y: [-5, 5, -5] }}
                transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
                className="absolute -top-4 -right-4 bg-white p-3 rounded-xl shadow-lg border border-gray-100"
              >
                <div className="text-center">
                  <GraduationCap className="h-6 w-6 text-blue-600 mx-auto mb-1" />
                  <div className="text-xs font-medium text-gray-900">BRAC University</div>
                </div>
              </motion.div>
              
              <motion.div
                animate={{ y: [5, -5, 5] }}
                transition={{ duration: 3.5, repeat: Infinity, ease: "easeInOut" }}
                className="absolute -bottom-4 -left-4 bg-white p-3 rounded-xl shadow-lg border border-gray-100"
              >
                <div className="text-center">
                  <Brain className="h-6 w-6 text-emerald-600 mx-auto mb-1" />
                  <div className="text-xs font-medium text-gray-900">AI Researcher</div>
                </div>
              </motion.div>
              
              <motion.div
                animate={{ y: [-3, 7, -3] }}
                transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
                className="absolute top-1/2 -right-6 bg-white p-3 rounded-xl shadow-lg border border-gray-100"
              >
                <div className="text-center">
                  <Cpu className="h-6 w-6 text-purple-600 mx-auto mb-1" />
                  <div className="text-xs font-medium text-gray-900">IoT Expert</div>
                </div>
              </motion.div>
            </div>
          </motion.div>

          {/* Founder Content */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <div className="mb-6">
              <span className="inline-block px-4 py-2 bg-blue-100 text-blue-800 text-sm font-medium rounded-full mb-4">
                Meet Our Founder
              </span>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Polok Poddar
              </h2>
              <p className="text-lg font-semibold text-blue-600 mb-4">
                AI Researcher, Embedded & IoT Developer, and Robotics Educator
              </p>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 mb-6">
              <blockquote className="text-lg text-gray-700 italic mb-4">
                "I believe every child has the potential to become a tech innovator. My mission is to make 
                cutting-edge technology education accessible, engaging, and inspiring for the next generation."
              </blockquote>
              <div className="text-sm text-gray-500">— Polok Poddar, Founder & Lead Instructor</div>
            </div>

            <div className="space-y-3 mb-6">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
                <span className="text-gray-700">Passionate about building intelligent solutions</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
                <span className="text-gray-700">Inspiring the next generation of tech leaders</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
                <span className="text-gray-700">BRAC University graduate with strong foundation</span>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <a 
                href="https://iamproloy.vercel.app/" 
                target="_blank" 
                rel="noopener noreferrer"
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-emerald-600 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
              >
                View Portfolio
                <ExternalLink className="ml-2 h-4 w-4" />
              </a>
              <a 
                href="/about" 
                className="inline-flex items-center px-6 py-3 border border-gray-300 text-gray-700 font-semibold rounded-lg hover:bg-gray-50 transition-colors duration-300"
              >
                Learn More
              </a>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default FounderSection;
