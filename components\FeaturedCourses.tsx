'use client';

import { motion } from 'framer-motion';
import { courses } from '@/lib/data';
import CourseCard from './CourseCard';

interface FeaturedCoursesProps {
  onEnrollClick?: (course: any) => void;
}

const FeaturedCourses = ({ onEnrollClick }: FeaturedCoursesProps) => {
  const featuredCourses = courses.filter(course => course.featured);

  return (
    <section className="section-padding bg-gray-50">
      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-secondary-900 mb-4">
            Featured Courses
          </h2>
          <p className="text-lg text-secondary-600 max-w-2xl mx-auto">
            Start your journey with our most popular courses designed for different skill levels and age groups.
          </p>
        </motion.div>

        {/* Courses Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {featuredCourses.map((course, index) => (
            <motion.div
              key={course.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <CourseCard
                course={course}
                showSyllabus={true}
                onEnrollClick={() => onEnrollClick?.(course)}
              />
            </motion.div>
          ))}
        </div>

        {/* View All Courses Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="text-center mt-12"
        >
          <a
            href="/courses"
            className="btn-secondary inline-flex items-center group"
          >
            View All Courses
            <svg
              className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-200"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M17 8l4 4m0 0l-4 4m4-4H3"
              />
            </svg>
          </a>
        </motion.div>
      </div>
    </section>
  );
};

export default FeaturedCourses;

