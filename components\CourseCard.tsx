'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { Clock, Users, ChevronUp, BookOpen } from 'lucide-react';
import { Course } from '../lib/data';

interface CourseCardProps {
  course: Course;
  showSyllabus?: boolean;
  onEnrollClick?: () => void;
}

const CourseCard = ({ course, showSyllabus = false, onEnrollClick }: CourseCardProps) => {
  const [isSyllabusOpen, setIsSyllabusOpen] = useState(false);

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'python':
        return 'bg-blue-100 text-blue-800';
      case 'robotics':
        return 'bg-green-100 text-green-800';
      case 'ml':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'python':
        return '🐍';
      case 'robotics':
        return '🤖';
      case 'ml':
        return '⚡';
      default:
        return '📚';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="card overflow-hidden group"
    >
      {/* Course Image */}
      <div className="relative overflow-hidden">
        <img
          src={course.image}
          alt={course.title}
          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
        />
        {course.featured && (
          <div className="absolute top-4 left-4 bg-accent-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
            Featured
          </div>
        )}
        <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium text-secondary-700">
          {getCategoryIcon(course.category)}
        </div>
      </div>

      {/* Course Content */}
      <div className="p-6">
        {/* Category Badge */}
        <div className="mb-3">
          <span className={`inline-block px-3 py-1 rounded-full text-xs font-medium ${getCategoryColor(course.category)}`}>
            {course.category.toUpperCase()}
          </span>
        </div>

        {/* Course Title */}
        <h3 className="text-xl font-bold text-secondary-900 mb-2 group-hover:text-primary-600 transition-colors duration-200">
          {course.title}
        </h3>

        {/* Course Description */}
        <p className="text-secondary-600 mb-4 line-clamp-2">
          {course.description}
        </p>

        {/* Course Details */}
        <div className="flex items-center justify-between mb-4 text-sm text-secondary-500">
          <div className="flex items-center">
            <Clock className="h-4 w-4 mr-1" />
            {course.duration}
          </div>
          <div className="flex items-center">
            <Users className="h-4 w-4 mr-1" />
            {course.ageGroup}
          </div>
        </div>

        {/* Level Badge */}
        <div className="mb-4">
          <span className="inline-block px-3 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
            {course.level} Level
          </span>
        </div>

        {/* 🧠 PSYCHOLOGICAL PRICING */}
        <div className="mb-4">
          {/* Payment Options Comparison */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border border-blue-200 mb-3">
            <div className="text-center mb-3">
              <div className="text-xs font-semibold text-blue-800 mb-1">💳 PAYMENT OPTIONS</div>
            </div>

            {/* Monthly Payment Option */}
            <div className="bg-white p-3 rounded-lg border border-gray-200 mb-2">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm text-gray-600">Monthly Payment</div>
                  <div className="flex items-center gap-2">
                    <div className="text-lg font-bold text-blue-600">
                      ৳{course.paymentOptions.monthlyAmount?.toLocaleString()}/month
                    </div>
                    {course.paymentOptions.originalMonthlyAmount && (
                      <div className="text-sm text-gray-500 line-through">
                        ৳{course.paymentOptions.originalMonthlyAmount.toLocaleString()}
                      </div>
                    )}
                  </div>
                  <div className="text-xs text-green-600 font-semibold">
                    Save ৳{((course.paymentOptions.originalMonthlyAmount || 0) - (course.paymentOptions.monthlyAmount || 0)).toLocaleString()}/month!
                  </div>
                </div>
                <div className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                  Flexible
                </div>
              </div>
            </div>

            {/* One-time Payment Option */}
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-3 rounded-lg border-2 border-green-300 relative">
              <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                <span className="bg-red-500 text-white text-xs font-bold px-3 py-1 rounded-full">
                  🔥 BEST DEAL
                </span>
              </div>
              <div className="flex items-center justify-between mt-2">
                <div>
                  <div className="text-sm text-gray-600">One-time Payment</div>
                  <div className="flex items-center gap-2">
                    <div className="text-xl font-bold text-green-600">
                      ৳{course.price.toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-500 line-through">
                      ৳{course.originalPrice.toLocaleString()}
                    </div>
                  </div>
                  <div className="text-xs text-green-600 font-semibold">
                    💰 Save ৳{(course.originalPrice - course.price).toLocaleString()} instantly!
                  </div>
                </div>
                <div className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                  Save More
                </div>
              </div>
            </div>
          </div>


        </div>

        {/* Benefits */}
        <div className="mb-4 p-3 bg-green-50 rounded-lg border border-green-200">
          <div className="text-xs font-medium text-green-800 mb-2">What You Get:</div>
          <div className="flex flex-wrap gap-1">
            {course.benefits.slice(0, 2).map((benefit, index) => (
              <span key={index} className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                {benefit}
              </span>
            ))}
            {course.benefits.length > 2 && (
              <span className="text-xs text-green-600">
                +{course.benefits.length - 2} more
              </span>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          {/* Enroll Button */}
          {onEnrollClick && (
            <button
              onClick={onEnrollClick}
              className="w-full bg-gradient-to-r from-green-600 to-emerald-600 text-white py-3 px-4 rounded-lg font-semibold hover:from-green-700 hover:to-emerald-700 transition-all duration-300 transform hover:scale-105 shadow-lg"
            >
              Enroll Now
            </button>
          )}

          <div className="flex gap-3">
            <Link
              href={`/courses/${course.id}`}
              className="flex-1 btn-primary text-center"
            >
              View Details
            </Link>

            {showSyllabus && (
              <button
                onClick={() => setIsSyllabusOpen(!isSyllabusOpen)}
                className="btn-secondary flex items-center justify-center"
              >
                {isSyllabusOpen ? (
                  <>
                    <ChevronUp className="h-4 w-4 mr-1" />
                    Hide
                  </>
                ) : (
                  <>
                    <BookOpen className="h-4 w-4 mr-1" />
                    Syllabus
                  </>
                )}
              </button>
            )}
          </div>
        </div>

        {/* Syllabus Preview */}
        {showSyllabus && isSyllabusOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="mt-4 pt-4 border-t border-gray-200"
          >
            <h4 className="font-semibold text-secondary-900 mb-3">Course Syllabus</h4>
            <div className="space-y-2">
              {course.syllabus.slice(0, 6).map((item, index) => (
                <div key={index} className="flex items-start">
                  <div className="w-8 h-8 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5">
                    {item.classNumber}
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-secondary-900 text-sm">{item.title}</div>
                    <div className="text-secondary-600 text-xs">{item.description}</div>
                  </div>
                </div>
              ))}
              {course.syllabus.length > 6 && (
                                 <div className="text-center pt-2">
                   <span className="text-primary-600 text-sm font-medium">
                     +{course.syllabus.length - 6} more classes
                   </span>
                 </div>
              )}
            </div>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};

export default CourseCard;
