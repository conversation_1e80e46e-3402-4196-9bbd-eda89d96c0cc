'use client';

import { motion } from 'framer-motion';
import { Play, Award, Code, Cpu } from 'lucide-react';

const StudentProjects = () => {
  const projects = [
    {
      id: 1,
      title: "Temperature Monitoring System",
      description: "Arduino-based temperature sensor with LCD display showing real-time temperature readings and alerts.",
      videoSrc: "/videos/robotics project _ working with temperature arduino and display.mp4",
      category: "Robotics",
      student: "Advanced Robotics Student",
      skills: ["Arduino Programming", "Sensor Integration", "LCD Display", "Temperature Monitoring"]
    },
    {
      id: 2,
      title: "LED Pattern Controller",
      description: "Creative LED light patterns using Arduino loops and timing functions for dynamic visual effects.",
      videoSrc: "/videos/robotics projects LED user different time use loop.mp4",
      category: "Robotics",
      student: "Basics of Robotics Student",
      skills: ["Arduino Programming", "LED Control", "Loop Functions", "Timing Control"]
    },
    {
      id: 3,
      title: "Water Level Detection System",
      description: "Smart water level monitoring system using sensors and Arduino for automated water management.",
      videoSrc: "/videos/robotics projects working with arduino and water lavel test.mp4",
      category: "Robotics",
      student: "Advanced Robotics Student",
      skills: ["Sensor Technology", "Arduino Programming", "Water Level Detection", "Automation"]
    },
    {
      id: 4,
      title: "Advanced Water Level Monitor",
      description: "Enhanced water level testing system with improved accuracy and real-time monitoring capabilities.",
      videoSrc: "/videos/water_level_test_robotics projects.mp4",
      category: "Robotics",
      student: "Advanced Robotics Student",
      skills: ["Advanced Sensors", "Data Processing", "Real-time Monitoring", "System Integration"]
    },
    {
      id: 5,
      title: "dotPY Academy Showcase",
      description: "Student project showcase demonstrating various programming and robotics achievements at dotPY Academy.",
      videoSrc: "/videos/dotPY Academy- Posts.mp4",
      category: "Showcase",
      student: "Multiple Students",
      skills: ["Python Programming", "Robotics", "Project Presentation", "Innovation"]
    }
  ];

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 text-sm font-medium rounded-full mb-4">
            <Award className="h-4 w-4 mr-2" />
            Student Achievements
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Amazing Student Projects
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            See what our students have built! From temperature monitoring systems to LED controllers, 
            our students create real-world projects that solve actual problems.
          </p>
        </motion.div>

        {/* Projects Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project, index) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden group"
            >
              {/* Video Container */}
              <div className="relative aspect-video bg-gray-100 overflow-hidden">
                <video
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  autoPlay
                  muted
                  loop
                  playsInline
                  preload="metadata"
                >
                  <source src={project.videoSrc} type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
                
                {/* Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-4 left-4 right-4">
                    <div className="flex items-center text-white text-sm">
                      <Play className="h-4 w-4 mr-2" />
                      <span>Student Project Demo</span>
                    </div>
                  </div>
                </div>

                {/* Category Badge */}
                <div className="absolute top-4 left-4">
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                    project.category === 'Robotics' 
                      ? 'bg-blue-100 text-blue-800' 
                      : 'bg-emerald-100 text-emerald-800'
                  }`}>
                    {project.category}
                  </span>
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-300">
                  {project.title}
                </h3>
                <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                  {project.description}
                </p>

                {/* Student Info */}
                <div className="flex items-center mb-4">
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-emerald-500 rounded-full flex items-center justify-center">
                    <Code className="h-4 w-4 text-white" />
                  </div>
                  <div className="ml-3">
                    <div className="text-sm font-medium text-gray-900">{project.student}</div>
                    <div className="text-xs text-gray-500">dotPY Academy Student</div>
                  </div>
                </div>

                {/* Skills Tags */}
                <div className="flex flex-wrap gap-2">
                  {project.skills.slice(0, 3).map((skill, skillIndex) => (
                    <span
                      key={skillIndex}
                      className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-md"
                    >
                      {skill}
                    </span>
                  ))}
                  {project.skills.length > 3 && (
                    <span className="px-2 py-1 bg-gray-100 text-gray-500 text-xs rounded-md">
                      +{project.skills.length - 3} more
                    </span>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="text-center mt-12"
        >
          <div className="bg-gradient-to-r from-blue-50 to-emerald-50 rounded-2xl p-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Ready to Build Amazing Projects?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Join our students in creating innovative solutions! From robotics to AI, 
              you'll build real projects that make a difference.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/courses"
                className="btn-primary inline-flex items-center"
              >
                <Cpu className="h-5 w-5 mr-2" />
                Explore Courses
              </a>
              <a
                href="/contact"
                className="btn-secondary inline-flex items-center"
              >
                Get Started Today
              </a>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default StudentProjects;
