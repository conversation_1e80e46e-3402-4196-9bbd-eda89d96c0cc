import React from 'react';
import { notFound } from 'next/navigation';
import { courses } from '../../../lib/data';
import CourseDetailsClient from './CourseDetailsClient';

interface PageProps {
  params: {
    id: string;
  };
}

// Generate static params for all courses
export async function generateStaticParams() {
  return courses.map((course) => ({
    id: course.id,
  }));
}

export default function CourseDetailsPage({ params }: PageProps) {
  const course = courses.find(c => c.id === params.id);

  if (!course) {
    notFound();
  }

  return <CourseDetailsClient course={course} />;
}
