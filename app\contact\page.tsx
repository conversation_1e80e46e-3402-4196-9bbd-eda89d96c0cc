'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Mail, Phone, MapPin, MessageCircle, Send, CheckCircle, Clock } from 'lucide-react';
import { courses } from '../../lib/data';
import EmailService from '../../lib/email-service';

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    age: '',
    interestedCourse: '',
    message: ''
  });
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState('');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitMessage('');

    try {
      // Send emails using our email service
      const result = await EmailService.sendContactFormEmails(formData);

      if (result.success) {
        setIsSubmitted(true);
        setSubmitMessage('Thank you! We\'ve sent you a confirmation email and will contact you within 24 hours.');

        // Reset form after 5 seconds
        setTimeout(() => {
          setIsSubmitted(false);
          setSubmitMessage('');
          setFormData({
            name: '',
            email: '',
            age: '',
            interestedCourse: '',
            message: ''
          });
        }, 5000);
      } else {
        setSubmitMessage('Message sent! We\'ll contact you soon, but there was an issue with the email confirmation.');
        setIsSubmitted(true);

        setTimeout(() => {
          setIsSubmitted(false);
          setSubmitMessage('');
        }, 4000);
      }
    } catch (error) {
      console.error('Form submission error:', error);
      setSubmitMessage('Your message has been received! We\'ll contact you within 24 hours.');
      setIsSubmitted(true);

      setTimeout(() => {
        setIsSubmitted(false);
        setSubmitMessage('');
      }, 4000);
    } finally {
      setIsSubmitting(false);
    }
  };

  const contactInfo = [
    {
      icon: Mail,
      title: 'Email',
      value: '<EMAIL>',
      link: 'mailto:<EMAIL>'
    },
    {
      icon: Phone,
      title: 'Phone',
      value: '+8801770065234',
      link: 'tel:+8801770065234'
    },
    {
      icon: MapPin,
      title: 'Address',
      value: 'Gulshan 1, Dhaka, Bangladesh',
      link: '#'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-secondary-900 mb-4">
              Contact & Register
            </h1>
            <p className="text-lg text-secondary-600 max-w-3xl mx-auto">
              Ready to start your journey with DOTPY Academy? Get in touch with us or register for your preferred course. 
              We're here to help you succeed.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid lg:grid-cols-2 gap-16">
          {/* Contact Form */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-3xl font-bold text-secondary-900 mb-6">
              Get Started Today
            </h2>
            
            {isSubmitted ? (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="bg-gradient-to-br from-green-50 to-blue-50 border border-green-200 rounded-xl p-8 text-center"
              >
                <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-green-800 mb-4">
                  🎉 Message Sent Successfully!
                </h3>

                <div className="space-y-4 text-left max-w-md mx-auto">
                  <div className="bg-white p-4 rounded-lg border border-green-200">
                    <h4 className="font-semibold text-green-800 mb-2">📧 What Happens Next:</h4>
                    <ul className="text-sm text-green-700 space-y-1">
                      <li>✅ Auto-reply email sent to your inbox</li>
                      <li>📞 Our team will call you within 24 hours</li>
                      <li>📅 FREE demo class invitation (if interested)</li>
                      <li>🎯 Personalized course recommendations</li>
                    </ul>
                  </div>

                  <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                    <h4 className="font-semibold text-blue-800 mb-2">📞 Urgent Questions?</h4>
                    <p className="text-sm text-blue-700">
                      Call us directly: <strong>+8801770065234</strong>
                    </p>
                  </div>
                </div>

                <div className="mt-6">
                  <Clock className="h-5 w-5 text-gray-500 inline-block mr-2" />
                  <span className="text-sm text-gray-600">
                    Check your email for confirmation details
                  </span>
                </div>
              </motion.div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-secondary-700 mb-2">
                      Full Name *
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      required
                      value={formData.name}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      placeholder="Enter your full name"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="age" className="block text-sm font-medium text-secondary-700 mb-2">
                      Age *
                    </label>
                    <input
                      type="number"
                      id="age"
                      name="age"
                      required
                      min="6"
                      max="20"
                      value={formData.age}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      placeholder="Enter your age"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-secondary-700 mb-2">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    required
                    value={formData.email}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder="Enter your email address"
                  />
                </div>

                <div>
                  <label htmlFor="interestedCourse" className="block text-sm font-medium text-secondary-700 mb-2">
                    Interested Course *
                  </label>
                  <select
                    id="interestedCourse"
                    name="interestedCourse"
                    required
                    value={formData.interestedCourse}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  >
                    <option value="">Select a course</option>
                    {courses.map((course) => (
                      <option key={course.id} value={course.id}>
                        {course.title} - ${course.price}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-secondary-700 mb-2">
                    Additional Message
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    rows={4}
                    value={formData.message}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder="Tell us about your learning goals or any questions you have..."
                  />
                </div>

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className={`w-full flex items-center justify-center py-3 px-6 rounded-lg font-semibold transition-all duration-300 ${
                    isSubmitting
                      ? 'bg-gray-400 cursor-not-allowed'
                      : 'btn-primary hover:scale-105'
                  }`}
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      Sending Message & Auto-Reply...
                    </>
                  ) : (
                    <>
                      <Send className="h-5 w-5 mr-2" />
                      Submit Registration
                    </>
                  )}
                </button>

                {/* Submit Message */}
                {submitMessage && (
                  <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <p className="text-blue-800 text-sm">{submitMessage}</p>
                  </div>
                )}
              </form>
            )}
          </motion.div>

          {/* Contact Information */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-3xl font-bold text-secondary-900 mb-6">
              Get in Touch
            </h2>
            
            <div className="space-y-6 mb-8">
              {contactInfo.map((info, index) => (
                <div key={index} className="flex items-start">
                  <div className="bg-primary-50 w-12 h-12 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                    <info.icon className="h-6 w-6 text-primary-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-secondary-900 mb-1">
                      {info.title}
                    </h3>
                    <a
                      href={info.link}
                      className="text-secondary-600 hover:text-primary-600 transition-colors duration-200"
                    >
                      {info.value}
                    </a>
                  </div>
                </div>
              ))}
            </div>

            {/* WhatsApp Contact */}
            <div className="bg-green-50 border border-green-200 rounded-xl p-6 mb-8">
              <div className="flex items-center mb-4">
                <MessageCircle className="h-6 w-6 text-green-600 mr-3" />
                <h3 className="font-semibold text-green-800">
                  Quick WhatsApp Contact
                </h3>
              </div>
              <p className="text-green-700 mb-4">
                Prefer to chat? Send us a WhatsApp message for instant support.
              </p>
              <a
                href="https://wa.me/8801770065234?text=Hi! I'm interested in DOTPY Academy courses."
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors duration-200 font-medium"
              >
                <MessageCircle className="h-5 w-5 mr-2" />
                Chat on WhatsApp
              </a>
            </div>

            {/* Office Hours */}
            <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
              <h3 className="font-semibold text-blue-800 mb-4">
                Office Hours
              </h3>
              <div className="space-y-2 text-blue-700">
                <div className="flex justify-between">
                  <span>Monday - Thursday:</span>
                  <span>9:00 AM - 6:00 PM</span>
                </div>
                <div className="flex justify-between">
                  <span>Friday:</span>
                  <span>9:00 AM - 8:00 PM</span>
                </div>
                <div className="flex justify-between">
                  <span>Saturday:</span>
                  <span>9:00 AM - 6:00 PM</span>
                </div>
                <div className="flex justify-between">
                  <span>Sunday:</span>
                  <span>Closed</span>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}

