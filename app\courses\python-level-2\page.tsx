'use client';

import React, { useState } from 'react';
import { courses } from '@/lib/data';
import { notFound } from 'next/navigation';
import EnrollmentForm from '@/components/EnrollmentForm';

export default function PythonLevel2() {
  const course = courses.find(c => c.id === 'python-level2');
  const [isEnrollmentFormOpen, setIsEnrollmentFormOpen] = useState(false);

  if (!course) {
    notFound();
  }

  const handleEnrollClick = () => {
    setIsEnrollmentFormOpen(true);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center mb-12">
            <span className="inline-block px-4 py-2 bg-yellow-100 text-yellow-800 text-sm font-medium rounded-full mb-4">
              {course.level}
            </span>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              {course.title}
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-8">
              {course.description}
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-2xl mx-auto">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{course.ageGroup}</div>
                <div className="text-sm text-gray-500">Age Group</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{course.duration}</div>
                <div className="text-sm text-gray-500">Duration</div>
              </div>
              <div className="text-center">
                <div className="flex flex-col items-center">
                  <div className="text-2xl font-bold text-green-600">৳{course.price.toLocaleString()}</div>
                  <div className="text-lg text-gray-500 line-through">৳{course.originalPrice.toLocaleString()}</div>
                  <div className="text-xs text-green-600 font-medium">Save ৳{(course.originalPrice - course.price).toLocaleString()}</div>
                </div>
                <div className="text-sm text-gray-500">BDT</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Syllabus Section */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <h2 className="text-2xl font-bold text-gray-900 mb-8">Course Syllabus</h2>

        <div className="space-y-4">
          {course.syllabus.map((item, index) => (
            <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                    {item.classNumber}
                  </div>
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {item.title}
                  </h3>
                  <p className="text-gray-600 mb-3">
                    {item.description}
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {item.topics.map((topic, topicIndex) => (
                      <span
                        key={topicIndex}
                        className="px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded"
                      >
                        {topic}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Pricing & Benefits Section */}
        <div className="mt-12 grid md:grid-cols-2 gap-8">
          {/* Pricing */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Course Pricing</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Original Price:</span>
                <span className="text-lg text-gray-500 line-through">৳{course.originalPrice.toLocaleString()}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Discounted Price:</span>
                <span className="text-2xl font-bold text-green-600">৳{course.price.toLocaleString()}</span>
              </div>
              <div className="flex items-center justify-between border-t pt-4">
                <span className="font-medium text-green-600">You Save:</span>
                <span className="text-xl font-bold text-green-600">৳{(course.originalPrice - course.price).toLocaleString()}</span>
              </div>
              {course.paymentOptions.monthly && (
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="text-sm font-medium text-blue-800 mb-2">Payment Options:</div>
                  <div className="space-y-1 text-sm text-blue-700">
                    <div>• One-time payment: ৳{course.price.toLocaleString()}</div>
                    <div>• Monthly payment: ৳{course.paymentOptions.monthlyAmount?.toLocaleString()}/month</div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Benefits */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-xl font-bold text-gray-900 mb-4">What You Get</h3>
            <div className="space-y-3">
              {course.benefits.map((benefit, index) => (
                <div key={index} className="flex items-start">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span className="text-gray-700">{benefit}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Enrollment CTA */}
        <div className="mt-12 text-center">
          <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-8 border border-green-200">
            <h3 className="text-xl font-bold text-gray-900 mb-4">
              Ready to Continue Your Python Journey?
            </h3>
            <p className="text-gray-600 mb-6">
              Join our {course.title} course and master object-oriented programming!
              Get certificate and special gifts upon completion.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={handleEnrollClick}
                className="btn-primary inline-flex items-center"
              >
                Enroll Now - ৳{course.price.toLocaleString()}
                <svg
                  className="ml-2 h-5 w-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M17 8l4 4m0 0l-4 4m4-4H3"
                  />
                </svg>
              </button>
              <a
                href="/contact"
                className="btn-secondary inline-flex items-center"
              >
                Monthly Plan - ৳{course.paymentOptions.monthlyAmount?.toLocaleString()}/month
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Enrollment Form */}
      <EnrollmentForm
        isOpen={isEnrollmentFormOpen}
        onClose={() => setIsEnrollmentFormOpen(false)}
        selectedCourse={course}
      />
    </div>
  );
}
