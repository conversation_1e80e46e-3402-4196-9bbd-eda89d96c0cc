'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';
import { ArrowRight, Star, Calendar } from 'lucide-react';

interface HeroProps {
  onDemoClick?: () => void;
  onEnrollClick?: () => void;
}

const Hero = ({ onDemoClick, onEnrollClick }: HeroProps) => {
  return (
    <section className="bg-gradient-hero min-h-screen flex items-center relative overflow-hidden pt-20">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-100 rounded-full opacity-20 blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-emerald-100 rounded-full opacity-20 blur-3xl"></div>
      </div>

      <div className="container-custom relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center lg:text-left"
          >
            {/* dotPY Academy Banner */}
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.1 }}
              className="inline-block bg-gradient-to-r from-blue-600 to-emerald-600 text-white px-6 py-3 rounded-full mb-6 shadow-lg"
            >
              <span className="font-semibold text-sm md:text-base">🎓 Welcome to dotPY ACADEMY - Where Future Coders Begin!</span>
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-4xl md:text-5xl lg:text-6xl font-bold text-secondary-900 mb-6"
            >
              <span className="text-gradient">Coding.</span>
              <br />
              <span className="text-gradient">Robotics.</span>
              <br />
              <span className="text-gradient">Future.</span>
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="text-lg md:text-xl text-secondary-600 mb-8 max-w-2xl mx-auto lg:mx-0"
            >
              Inspire your child's future with cutting-edge technology education.
              Our expert instructors guide young minds through Python programming,
              robotics, and emerging technologies in a fun, engaging environment.
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
            >
              {/* Demo Class Button */}
              <button
                onClick={onDemoClick}
                className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-8 py-4 rounded-full font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group"
              >
                <Star className="inline-block mr-2 h-5 w-5" />
                FREE Demo Class
                <Calendar className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-200" />
              </button>

              {/* Enroll Button */}
              <button
                onClick={onEnrollClick}
                className="btn-primary group transform hover:scale-105 transition-all duration-300"
              >
                Enroll Now
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-200" />
              </button>
            </motion.div>

            {/* Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="grid grid-cols-2 gap-8 mt-12 max-w-sm mx-auto lg:mx-0"
            >
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600">100+</div>
                <div className="text-sm text-gray-500">Happy Students</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600">95%</div>
                <div className="text-sm text-gray-500">Success Rate</div>
              </div>
            </motion.div>
          </motion.div>

          {/* Right Content - Logo Animation */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="relative flex items-center justify-center"
          >
            <motion.div
              animate={{
                scale: [1, 1.05, 1],
                rotate: [0, 2, -2, 0]
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="relative"
            >
              {/* Logo with professional animation */}
              <div className="bg-white p-8 rounded-3xl shadow-2xl border border-gray-100">
                <img
                  src="/logo.svg"
                  alt="dotPY Academy Logo"
                  className="w-120 h-64 object-contain"
                />
              </div>

              {/* Subtle glow effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-200 to-emerald-200 rounded-3xl opacity-20 blur-xl -z-10"></div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default Hero;

