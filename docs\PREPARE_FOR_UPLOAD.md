# 📦 Prepare Files for cPanel Upload

## 🎯 **EASY UPLOAD METHOD**

### **Option 1: ZIP Upload (Recommended)**

1. **Go to your `out` folder**: `D:\dot\out\`

2. **Select ALL files and folders inside `out`**:
   - Select everything (Ctrl+A)
   - Right-click → "Send to" → "Compressed folder"
   - Name it: `dotpy-academy-website.zip`

3. **Upload the ZIP to cPanel**:
   - <PERSON>gin to cPanel → File Manager
   - Go to `public_html`
   - Upload the ZIP file
   - Right-click ZIP → Extract
   - Delete the ZIP file after extraction

### **Option 2: Direct Upload**

1. **Select all files in `out` folder**
2. **Upload directly to cPanel File Manager**
3. **Upload to `public_html` directory**

---

## 📋 **FILE STRUCTURE AFTER UPLOAD**

Your `public_html` should look like this:

```
public_html/
├── index.html
├── 404.html
├── _next/
│   ├── static/
│   └── [build files]
├── about/
│   └── index.html
├── contact/
│   └── index.html
├── courses/
│   ├── index.html
│   ├── python-level1/
│   ├── python-level2/
│   ├── python-level3/
│   ├── basics-robotics/
│   └── ml-algorithms/
├── projects/
│   └── index.html
├── videos/
├── workshop/
├── founder.jpg
└── logo.svg
```

---

## ⚠️ **IMPORTANT NOTES**

### **DO NOT UPLOAD:**
- ❌ The `out` folder itself
- ❌ Node.js files (`package.json`, etc.)
- ❌ Source code (`app`, `components`, etc.)

### **DO UPLOAD:**
- ✅ Everything INSIDE the `out` folder
- ✅ All HTML files
- ✅ The `_next` folder (contains CSS/JS)
- ✅ All image and video files

---

## 🔧 **TROUBLESHOOTING**

### **If website looks broken:**
1. Check if `_next` folder uploaded correctly
2. Verify file permissions (644 for files, 755 for folders)
3. Clear browser cache and refresh

### **If pages show 404:**
1. Check folder structure matches exactly
2. Ensure `index.html` exists in each folder
3. Verify file names are correct (case-sensitive)

---

## ✅ **READY TO DEPLOY!**

Your website files are optimized and ready for any cPanel hosting provider!

**Total file size**: ~50MB (including videos)
**Pages generated**: 14 static pages
**Load time**: Fast (optimized static files)
**Compatibility**: Works with all hosting providers
