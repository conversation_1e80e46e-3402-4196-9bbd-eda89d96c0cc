'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Clock, Users, Star, Shield, Gift, Trophy, Zap, Heart } from 'lucide-react';
import { Course } from '@/lib/data';

interface PsychologyBoosterProps {
  course: Course;
}

const PsychologyBooster = ({ course }: PsychologyBoosterProps) => {
  return (
    <div className="space-y-8">
      {/* 🔥 URGENCY BANNER */}
      {course.urgencyText && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-gradient-to-r from-red-500 to-orange-500 text-white p-6 rounded-lg shadow-lg border-2 border-red-300"
        >
          <div className="flex items-center justify-center space-x-3">
            <Zap className="h-6 w-6 animate-pulse" />
            <p className="text-lg font-bold text-center">{course.urgencyText}</p>
            <Zap className="h-6 w-6 animate-pulse" />
          </div>
        </motion.div>
      )}

      {/* 📊 SOCIAL PROOF SECTION */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-green-50 to-blue-50 p-6 rounded-lg border border-green-200"
      >
        <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
          <Trophy className="h-6 w-6 text-yellow-500 mr-2" />
          Trusted by Hundreds of Families
        </h3>
        <div className="grid grid-cols-3 gap-4 text-center">
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <div className="flex items-center justify-center mb-2">
              <Users className="h-6 w-6 text-blue-500" />
            </div>
            <p className="text-2xl font-bold text-blue-600">{course.socialProof.studentsEnrolled}</p>
            <p className="text-sm text-gray-600">Happy Students</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <div className="flex items-center justify-center mb-2">
              <Star className="h-6 w-6 text-yellow-500" />
            </div>
            <p className="text-2xl font-bold text-yellow-600">{course.socialProof.averageRating}/5</p>
            <p className="text-sm text-gray-600">Average Rating</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <div className="flex items-center justify-center mb-2">
              <Trophy className="h-6 w-6 text-green-500" />
            </div>
            <p className="text-2xl font-bold text-green-600">{course.socialProof.successRate}%</p>
            <p className="text-sm text-gray-600">Success Rate</p>
          </div>
        </div>
      </motion.div>

      {/* ⚡ SCARCITY ELEMENTS */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-lg border border-purple-200"
      >
        <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
          <Clock className="h-6 w-6 text-purple-500 mr-2" />
          Limited Availability
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {course.scarcityElements.spotsLeft && (
            <div className="bg-white p-4 rounded-lg shadow-sm text-center">
              <p className="text-3xl font-bold text-red-600">{course.scarcityElements.spotsLeft}</p>
              <p className="text-sm text-gray-600">Spots Left</p>
            </div>
          )}
          {course.scarcityElements.nextBatchDate && (
            <div className="bg-white p-4 rounded-lg shadow-sm text-center">
              <p className="text-sm font-semibold text-gray-900">Next Batch</p>
              <p className="text-sm text-gray-600">{course.scarcityElements.nextBatchDate}</p>
            </div>
          )}
          {course.scarcityElements.earlyBirdDeadline && (
            <div className="bg-white p-4 rounded-lg shadow-sm text-center">
              <p className="text-sm font-semibold text-gray-900">Early Bird Ends</p>
              <p className="text-sm text-red-600 font-bold">{course.scarcityElements.earlyBirdDeadline}</p>
            </div>
          )}
        </div>
      </motion.div>

      {/* 🏆 AUTHORITY SIGNALS */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg border border-blue-200"
      >
        <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
          <Shield className="h-6 w-6 text-blue-500 mr-2" />
          Why Parents Trust Us
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {course.authoritySignals.map((signal, index) => (
            <div key={index} className="flex items-center space-x-3 bg-white p-3 rounded-lg shadow-sm">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <p className="text-sm text-gray-700">{signal}</p>
            </div>
          ))}
        </div>
      </motion.div>

      {/* 🛡️ RISK REVERSAL */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-emerald-50 to-teal-50 p-6 rounded-lg border border-emerald-200"
      >
        <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
          <Shield className="h-6 w-6 text-emerald-500 mr-2" />
          100% Risk-Free Guarantee
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white p-4 rounded-lg shadow-sm text-center">
            <Gift className="h-8 w-8 text-emerald-500 mx-auto mb-2" />
            <p className="font-semibold text-gray-900">{course.riskReversal.freeTrialClasses} FREE Classes</p>
            <p className="text-sm text-gray-600">Try before you buy</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm text-center">
            <Shield className="h-8 w-8 text-emerald-500 mx-auto mb-2" />
            <p className="font-semibold text-gray-900">{course.riskReversal.guaranteeDays}-Day Guarantee</p>
            <p className="text-sm text-gray-600">Full money back</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm text-center">
            <Heart className="h-8 w-8 text-emerald-500 mx-auto mb-2" />
            <p className="font-semibold text-gray-900">Love it or Leave it</p>
            <p className="text-sm text-gray-600">No questions asked</p>
          </div>
        </div>
      </motion.div>

      {/* 😰 EMOTIONAL TRIGGERS - PARENT WORRIES */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-yellow-50 to-orange-50 p-6 rounded-lg border border-yellow-200"
      >
        <h3 className="text-xl font-bold text-gray-900 mb-4">
          Are You Worried About...
        </h3>
        <div className="space-y-3">
          {course.emotionalTriggers.parentWorries.map((worry, index) => (
            <div key={index} className="bg-white p-4 rounded-lg shadow-sm">
              <p className="text-gray-700">{worry}</p>
            </div>
          ))}
        </div>
        <div className="mt-4 p-4 bg-green-100 rounded-lg">
          <p className="text-green-800 font-semibold">
            ✅ Our course solves ALL these worries! Your child will be ahead of the curve.
          </p>
        </div>
      </motion.div>

      {/* 🌟 CHILD DREAMS */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-pink-50 to-purple-50 p-6 rounded-lg border border-pink-200"
      >
        <h3 className="text-xl font-bold text-gray-900 mb-4">
          Your Child's Dreams Can Come True!
        </h3>
        <div className="space-y-3">
          {course.emotionalTriggers.childDreams.map((dream, index) => (
            <div key={index} className="bg-white p-4 rounded-lg shadow-sm">
              <p className="text-gray-700">{dream}</p>
            </div>
          ))}
        </div>
        <div className="mt-4 p-4 bg-blue-100 rounded-lg">
          <p className="text-blue-800 font-semibold">
            🚀 We turn dreams into reality! Join hundreds of kids living their tech dreams.
          </p>
        </div>
      </motion.div>

      {/* 🔥 FEAR OF MISSING OUT */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-red-50 to-pink-50 p-6 rounded-lg border border-red-200"
      >
        <h3 className="text-xl font-bold text-gray-900 mb-4">
          Don't Let Your Child Fall Behind!
        </h3>
        <div className="bg-white p-4 rounded-lg shadow-sm">
          <p className="text-gray-700 text-lg leading-relaxed">
            {course.emotionalTriggers.fearOfMissingOut}
          </p>
        </div>
        <div className="mt-4 text-center">
          <a
            href="/contact"
            className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-red-600 to-pink-600 text-white font-bold rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
          >
            🚀 SECURE YOUR CHILD'S SPOT NOW!
          </a>
        </div>
      </motion.div>
    </div>
  );
};

export default PsychologyBooster;
