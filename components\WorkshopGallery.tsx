'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Camera, Users, X, ChevronLeft, ChevronRight, Calendar, MapPin } from 'lucide-react';

const WorkshopGallery = () => {
  const [selectedImage, setSelectedImage] = useState<number | null>(null);

  const workshopImages = [
    {
      id: 1,
      src: "/workshop/IMG_0167.JPG",
      title: "Basics of Robotics - Arduino Programming",
      description: "Students learning Arduino programming fundamentals in our Basics of Robotics course, working with sensors and basic circuits.",
      category: "Basics of Robotics",
      participants: "12 students",
      date: "Recent Workshop"
    },
    {
      id: 2,
      src: "/workshop/IMG_0260.JPG",
      title: "Basics of Robotics - Circuit Building",
      description: "Hands-on circuit building session where students learn basic electronics and component connections in robotics.",
      category: "Basics of Robotics",
      participants: "15 students",
      date: "Recent Workshop"
    },
    {
      id: 3,
      src: "/workshop/IMG_0272.JPG",
      title: "Basics of Robotics - Team Projects",
      description: "Students collaborating on basic robotics projects, learning teamwork and problem-solving skills together.",
      category: "Basics of Robotics",
      participants: "10 students",
      date: "Recent Workshop"
    },
    {
      id: 4,
      src: "/workshop/IMG_3554.JPG",
      title: "Basics of Robotics - Sensor Integration",
      description: "Learning how to integrate various sensors with Arduino in our foundational robotics course.",
      category: "Basics of Robotics",
      participants: "8 students",
      date: "Recent Workshop"
    },
    {
      id: 5,
      src: "/workshop/IMG_3567.JPG",
      title: "Basics of Robotics - Robot Assembly",
      description: "Students assembling their first robots, learning mechanical construction and basic programming concepts.",
      category: "Basics of Robotics",
      participants: "14 students",
      date: "Recent Workshop"
    }
  ];

  const nextImage = () => {
    if (selectedImage !== null) {
      setSelectedImage((selectedImage + 1) % workshopImages.length);
    }
  };

  const prevImage = () => {
    if (selectedImage !== null) {
      setSelectedImage(selectedImage === 0 ? workshopImages.length - 1 : selectedImage - 1);
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Basics of Robotics':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 text-sm font-medium rounded-full mb-4">
            <Camera className="h-4 w-4 mr-2" />
            Basics of Robotics Workshop
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Robotics Learning in Action
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Take a glimpse into our Basics of Robotics workshops where students learn Arduino programming, circuit building,
            and robot assembly. See how we make robotics education hands-on, engaging, and accessible for beginners.
          </p>
        </motion.div>

        {/* Image Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {workshopImages.map((image, index) => (
            <motion.div
              key={image.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="group relative bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden cursor-pointer"
              onClick={() => setSelectedImage(index)}
            >
              {/* Image */}
              <div className="relative aspect-[4/3] overflow-hidden">
                <img
                  src={image.src}
                  alt={image.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  onError={(e) => {
                    // Fallback to a placeholder if image doesn't load
                    e.currentTarget.src = "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=400&h=300&fit=crop";
                  }}
                />
                
                {/* Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-4 left-4 right-4 text-white">
                    <div className="flex items-center text-sm mb-2">
                      <Camera className="h-4 w-4 mr-2" />
                      <span>Click to view</span>
                    </div>
                  </div>
                </div>

                {/* Category Badge */}
                <div className="absolute top-4 left-4">
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${getCategoryColor(image.category)}`}>
                    {image.category}
                  </span>
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-300">
                  {image.title}
                </h3>
                <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                  {image.description}
                </p>

                {/* Workshop Details */}
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <div className="flex items-center">
                    <Users className="h-3 w-3 mr-1" />
                    <span>{image.participants}</span>
                  </div>
                  <div className="flex items-center">
                    <Calendar className="h-3 w-3 mr-1" />
                    <span>{image.date}</span>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="text-center mt-12"
        >
          <div className="bg-gradient-to-r from-blue-50 to-emerald-50 rounded-2xl p-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Join Our Basics of Robotics Course!
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Start your robotics journey with our beginner-friendly course. Learn Arduino programming,
              circuit building, and robot assembly in our hands-on workshops.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/courses/basics-robotics"
                className="btn-primary inline-flex items-center"
              >
                <Users className="h-5 w-5 mr-2" />
                View Robotics Course
              </a>
              <a
                href="/contact"
                className="btn-secondary inline-flex items-center"
              >
                <MapPin className="h-5 w-5 mr-2" />
                Enroll Now
              </a>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Image Modal */}
      <AnimatePresence>
        {selectedImage !== null && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/90 flex items-center justify-center p-4 z-50"
            onClick={() => setSelectedImage(null)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="relative max-w-4xl w-full max-h-[90vh] bg-white rounded-xl overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Close Button */}
              <button
                onClick={() => setSelectedImage(null)}
                className="absolute top-4 right-4 z-10 bg-white/90 backdrop-blur-sm p-2 rounded-full hover:bg-white transition-colors duration-200"
              >
                <X className="h-6 w-6 text-gray-600" />
              </button>

              {/* Navigation Buttons */}
              <button
                onClick={prevImage}
                className="absolute left-4 top-1/2 -translate-y-1/2 z-10 bg-white/90 backdrop-blur-sm p-2 rounded-full hover:bg-white transition-colors duration-200"
              >
                <ChevronLeft className="h-6 w-6 text-gray-600" />
              </button>
              <button
                onClick={nextImage}
                className="absolute right-4 top-1/2 -translate-y-1/2 z-10 bg-white/90 backdrop-blur-sm p-2 rounded-full hover:bg-white transition-colors duration-200"
              >
                <ChevronRight className="h-6 w-6 text-gray-600" />
              </button>

              {/* Image */}
              <div className="aspect-[4/3] bg-gray-100">
                <img
                  src={workshopImages[selectedImage].src}
                  alt={workshopImages[selectedImage].title}
                  className="w-full h-full object-cover"
                />
              </div>

              {/* Image Details */}
              <div className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getCategoryColor(workshopImages[selectedImage].category)}`}>
                    {workshopImages[selectedImage].category}
                  </span>
                  <div className="flex items-center text-sm text-gray-500">
                    <Users className="h-4 w-4 mr-1" />
                    <span>{workshopImages[selectedImage].participants}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-500">
                    <Calendar className="h-4 w-4 mr-1" />
                    <span>{workshopImages[selectedImage].date}</span>
                  </div>
                </div>
                
                <h3 className="text-2xl font-bold text-gray-900 mb-3">
                  {workshopImages[selectedImage].title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {workshopImages[selectedImage].description}
                </p>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </section>
  );
};

export default WorkshopGallery;
