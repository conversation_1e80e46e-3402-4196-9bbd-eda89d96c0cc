'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X, 
  CheckCircle, 
  Calendar, 
  Users, 
  Clock, 
  AlertCircle,
  Info,
  BookOpen,
  Star
} from 'lucide-react';

interface DemoClassFormProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function DemoClassForm({ isOpen, onClose }: DemoClassFormProps) {
  const [showThankYou, setShowThankYou] = useState(false);
  const [isFormLoaded, setIsFormLoaded] = useState(false);
  const [currentEnrollments, setCurrentEnrollments] = useState(3); // Demo counter

  // Get next Friday date
  const getNextFriday = () => {
    const today = new Date();
    const dayOfWeek = today.getDay();
    const daysUntilFriday = (5 - dayOfWeek + 7) % 7 || 7; // 5 = Friday
    const nextFriday = new Date(today);
    nextFriday.setDate(today.getDate() + daysUntilFriday);
    return nextFriday.toLocaleDateString('en-US', { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  // Listen for form submission
  useEffect(() => {
    if (!isOpen) {
      setShowThankYou(false);
      setIsFormLoaded(false);
      return;
    }

    const handleMessage = (event: MessageEvent) => {
      if (event.data && typeof event.data === 'string') {
        if (event.data.includes('formResponse') || event.data.includes('submitted')) {
          setCurrentEnrollments(prev => prev + 1);
          setShowThankYou(true);
        }
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [isOpen]);

  const handleClose = () => {
    setShowThankYou(false);
    setIsFormLoaded(false);
    onClose();
  };

  const handleThankYouClose = () => {
    setShowThankYou(false);
    setTimeout(() => handleClose(), 500);
  };

  const spotsRemaining = Math.max(0, 5 - currentEnrollments);
  const isClassConfirmed = currentEnrollments >= 5;

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 overflow-y-auto">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-50"
          onClick={handleClose}
        />

        {/* Modal */}
        <div className="flex min-h-full items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative w-full max-w-4xl bg-white rounded-2xl shadow-2xl"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 flex items-center">
                  <Star className="h-6 w-6 text-yellow-500 mr-2" />
                  FREE Demo Class
                </h2>
                <p className="text-gray-600 mt-1">
                  Experience our teaching methodology before enrolling
                </p>
              </div>
              <button
                onClick={handleClose}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
              >
                <X className="h-6 w-6 text-gray-500" />
              </button>
            </div>

            {/* Demo Class Info */}
            <div className="p-6 bg-gradient-to-r from-blue-50 to-purple-50 border-b border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Schedule */}
                <div className="flex items-center">
                  <Calendar className="h-8 w-8 text-blue-600 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-500">Next Demo Class</p>
                    <p className="font-bold text-gray-900">{getNextFriday()}</p>
                    <p className="text-xs text-blue-600">Schedule will be confirmed</p>
                  </div>
                </div>

                {/* Enrollment Status */}
                <div className="flex items-center">
                  <Users className="h-8 w-8 text-green-600 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-500">Current Enrollments</p>
                    <p className="font-bold text-gray-900">{currentEnrollments} / 5 minimum</p>
                    <p className={`text-xs ${isClassConfirmed ? 'text-green-600' : 'text-orange-600'}`}>
                      {isClassConfirmed ? '✅ Class Confirmed!' : `${spotsRemaining} more needed`}
                    </p>
                  </div>
                </div>

                {/* Duration */}
                <div className="flex items-center">
                  <Clock className="h-8 w-8 text-purple-600 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-500">Duration</p>
                    <p className="font-bold text-gray-900">60 Minutes</p>
                    <p className="text-xs text-purple-600">Interactive Session</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Important Conditions */}
            <div className="p-6 bg-yellow-50 border-b border-gray-200">
              <div className="flex items-start">
                <AlertCircle className="h-6 w-6 text-yellow-600 mr-3 mt-0.5" />
                <div>
                  <h3 className="font-semibold text-yellow-800 mb-2">Important Demo Class Conditions:</h3>
                  <ul className="text-sm text-yellow-700 space-y-1">
                    <li>• <strong>Minimum Requirement:</strong> Demo class needs at least 5 students to proceed</li>
                    <li>• <strong>Schedule:</strong> Demo class schedule will be confirmed based on availability</li>
                    <li>• <strong>Confirmation:</strong> You'll receive confirmation 24 hours before the class</li>
                    <li>• <strong>Cancellation:</strong> If minimum enrollment isn't met, class will be rescheduled</li>
                    <li>• <strong>Free of Cost:</strong> No payment required for demo class</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Form Content */}
            <div className="relative">
              <AnimatePresence mode="wait">
                {showThankYou ? (
                  // Thank You Message
                  <motion.div
                    key="thankyou"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="p-12 text-center"
                  >
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                      className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6"
                    >
                      <CheckCircle className="h-10 w-10 text-green-600" />
                    </motion.div>
                    
                    <motion.h3
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.4 }}
                      className="text-2xl font-bold text-gray-900 mb-4"
                    >
                      Demo Class Registration Successful! 🎉
                    </motion.h3>
                    
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.6 }}
                      className="space-y-4 text-gray-600 mb-8"
                    >
                      <p className="text-lg">
                        Thank you for registering for our FREE demo class!
                      </p>
                      
                      <div className={`p-4 rounded-lg ${isClassConfirmed ? 'bg-green-50 border border-green-200' : 'bg-orange-50 border border-orange-200'}`}>
                        {isClassConfirmed ? (
                          <div className="text-green-800">
                            <p className="font-semibold">✅ Demo Class Confirmed!</p>
                            <p className="text-sm mt-1">
                              We have enough students. Your demo class is scheduled for <strong>{getNextFriday()}</strong>. Time will be confirmed.
                            </p>
                          </div>
                        ) : (
                          <div className="text-orange-800">
                            <p className="font-semibold">⏳ Waiting for More Students</p>
                            <p className="text-sm mt-1">
                              We need <strong>{spotsRemaining} more students</strong> to confirm the demo class. 
                              We'll notify you 24 hours before if the class proceeds.
                            </p>
                          </div>
                        )}
                      </div>

                      <div className="bg-blue-50 p-4 rounded-lg">
                        <h4 className="font-medium text-blue-900 mb-2">What's Next?</h4>
                        <ul className="text-sm text-blue-800 space-y-1">
                          <li>📧 Check your email for confirmation details</li>
                          <li>📱 We'll send you WhatsApp/SMS reminders</li>
                          <li>🔗 Demo class link will be shared 1 hour before</li>
                          <li>📚 Prepare to experience our interactive teaching style</li>
                        </ul>
                      </div>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.8 }}
                      className="flex flex-col sm:flex-row gap-3 justify-center"
                    >
                      <button
                        onClick={handleThankYouClose}
                        className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                      >
                        Explore Our Courses
                      </button>
                      <a
                        href="/courses"
                        className="px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium text-center"
                      >
                        View All Courses
                      </a>
                    </motion.div>
                  </motion.div>
                ) : (
                  // Google Form
                  <motion.div
                    key="form"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="relative"
                  >
                    {/* Loading overlay */}
                    {!isFormLoaded && (
                      <div className="absolute inset-0 bg-white flex items-center justify-center z-10">
                        <div className="text-center">
                          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                          <p className="text-gray-600">Loading demo class registration form...</p>
                        </div>
                      </div>
                    )}
                    
                    {/* Google Form Iframe */}
                    <iframe
                      src="https://docs.google.com/forms/d/e/1FAIpQLSfNNBL0oudMJdJYNb2z95vjaTcKVRR_tUTuJ6sD7hp-FoVfEw/viewform?embedded=true"
                      width="100%"
                      height="600"
                      frameBorder="0"
                      marginHeight={0}
                      marginWidth={0}
                      onLoad={() => setIsFormLoaded(true)}
                      className="w-full"
                      title="Demo Class Registration Form"
                    >
                      Loading demo class registration form...
                    </iframe>

                    {/* Manual Thank You Trigger */}
                    <div className="p-4 bg-gray-50 border-t text-center">
                      <p className="text-sm text-gray-600 mb-3">
                        Completed your registration? Click below for confirmation:
                      </p>
                      <button
                        onClick={() => setShowThankYou(true)}
                        className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-medium"
                      >
                        I've Registered for Demo Class
                      </button>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </motion.div>
        </div>
      </div>
    </AnimatePresence>
  );
}
