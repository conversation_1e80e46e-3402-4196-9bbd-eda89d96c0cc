// Email service for contact form submissions
// This works with any hosting provider including cPanel

interface ContactFormData {
  name: string;
  email: string;
  age: string;
  interestedCourse: string;
  message: string;
}

interface EmailResponse {
  success: boolean;
  message: string;
}

export class EmailService {
  // Your email configuration
  private static readonly ADMIN_EMAIL = '<EMAIL>'; // Replace with your email
  private static readonly FROM_EMAIL = '<EMAIL>'; // Replace with your domain email
  
  /**
   * Send email using EmailJS (Free service that works with any hosting)
   * Alternative: Use your cPanel email with a backend service
   */
  static async sendContactFormEmails(formData: ContactFormData): Promise<EmailResponse> {
    try {
      // Send auto-reply to user
      await this.sendAutoReplyToUser(formData);
      
      // Send notification to admin
      await this.sendNotificationToAdmin(formData);
      
      return {
        success: true,
        message: 'Emails sent successfully'
      };
    } catch (error) {
      console.error('Email sending failed:', error);
      return {
        success: false,
        message: 'Failed to send emails'
      };
    }
  }

  /**
   * Send auto-reply email to the user
   */
  private static async sendAutoReplyToUser(formData: ContactFormData): Promise<void> {
    const autoReplyContent = this.generateAutoReplyContent(formData);
    
    // Using EmailJS for demonstration (you can replace with your preferred email service)
    const emailData = {
      to_email: formData.email,
      to_name: formData.name,
      from_name: 'dotPY Academy',
      from_email: this.FROM_EMAIL,
      subject: 'Thank you for contacting dotPY Academy! 🎓',
      message: autoReplyContent.html,
      reply_to: this.ADMIN_EMAIL
    };

    // Simulate email sending (replace with actual email service)
    await this.simulateEmailSending(emailData);
  }

  /**
   * Send notification email to admin
   */
  private static async sendNotificationToAdmin(formData: ContactFormData): Promise<void> {
    const adminNotificationContent = this.generateAdminNotificationContent(formData);
    
    const emailData = {
      to_email: this.ADMIN_EMAIL,
      to_name: 'dotPY Academy Admin',
      from_name: 'Website Contact Form',
      from_email: this.FROM_EMAIL,
      subject: `New Contact Form Submission from ${formData.name}`,
      message: adminNotificationContent.html,
      reply_to: formData.email
    };

    // Simulate email sending (replace with actual email service)
    await this.simulateEmailSending(emailData);
  }

  /**
   * Generate auto-reply email content for users
   */
  private static generateAutoReplyContent(formData: ContactFormData) {
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #3B82F6, #10B981); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
          .highlight { background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          .button { display: inline-block; background: #3B82F6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎓 Thank You for Contacting dotPY Academy!</h1>
            <p>We're excited to help you start your coding journey!</p>
          </div>
          
          <div class="content">
            <h2>Hi ${formData.name}! 👋</h2>
            
            <p>Thank you for reaching out to dotPY Academy. We've received your inquiry and our team will get back to you within <strong>24 hours</strong>.</p>
            
            <div class="highlight">
              <h3>📋 Your Inquiry Details:</h3>
              <p><strong>Name:</strong> ${formData.name}</p>
              <p><strong>Email:</strong> ${formData.email}</p>
              <p><strong>Age:</strong> ${formData.age}</p>
              <p><strong>Interested Course:</strong> ${formData.interestedCourse || 'General Inquiry'}</p>
              <p><strong>Message:</strong> ${formData.message}</p>
            </div>

            <h3>🚀 What's Next?</h3>
            <ul>
              <li>📞 Our team will call you within 24 hours</li>
              <li>💬 We'll discuss your learning goals and course options</li>
              <li>📅 Schedule a FREE demo class if interested</li>
              <li>🎯 Get personalized course recommendations</li>
            </ul>

            <div style="text-align: center; margin: 30px 0;">
              <a href="http://localhost:3001/courses" class="button">Explore Our Courses</a>
              <a href="http://localhost:3001" class="button" style="background: #10B981;">Visit Our Website</a>
            </div>

            <div class="highlight">
              <h3>📞 Contact Information:</h3>
              <p><strong>Phone:</strong> +8801770065234</p>
              <p><strong>Email:</strong> <EMAIL></p>
              <p><strong>Address:</strong> Gulshan 1, Dhaka, Bangladesh</p>
              <p><strong>Demo Classes:</strong> Every Friday at 4:00 PM</p>
            </div>

            <p>If you have any urgent questions, feel free to call us directly at <strong>+8801770065234</strong>.</p>
            
            <p>Best regards,<br>
            <strong>Polok Poddar</strong><br>
            Founder & Lead Instructor<br>
            dotPY Academy</p>
          </div>
          
          <div class="footer">
            <p>© 2024 dotPY Academy. All rights reserved.</p>
            <p>Empowering the next generation with cutting-edge technology education.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    return { html };
  }

  /**
   * Generate admin notification email content
   */
  private static generateAdminNotificationContent(formData: ContactFormData) {
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #1f2937; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }
          .field { background: white; padding: 15px; margin: 10px 0; border-radius: 6px; border-left: 4px solid #3B82F6; }
          .urgent { border-left-color: #EF4444; background: #FEF2F2; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🔔 New Contact Form Submission</h1>
            <p>dotPY Academy Website</p>
          </div>
          
          <div class="content">
            <h2>📝 Contact Details:</h2>
            
            <div class="field">
              <strong>👤 Name:</strong> ${formData.name}
            </div>
            
            <div class="field">
              <strong>📧 Email:</strong> ${formData.email}
            </div>
            
            <div class="field">
              <strong>🎂 Age:</strong> ${formData.age}
            </div>
            
            <div class="field">
              <strong>📚 Interested Course:</strong> ${formData.interestedCourse || 'General Inquiry'}
            </div>
            
            <div class="field ${formData.message.toLowerCase().includes('urgent') ? 'urgent' : ''}">
              <strong>💬 Message:</strong><br>
              ${formData.message}
            </div>

            <div class="field">
              <strong>⏰ Submitted:</strong> ${new Date().toLocaleString('en-US', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              })}
            </div>

            <h3>🎯 Recommended Actions:</h3>
            <ul>
              <li>📞 Call ${formData.name} within 24 hours</li>
              <li>📧 Send personalized course information</li>
              <li>📅 Offer FREE demo class registration</li>
              <li>💰 Discuss pricing and payment options</li>
            </ul>

            <p><strong>Reply directly to this email to respond to ${formData.name}.</strong></p>
          </div>
        </div>
      </body>
      </html>
    `;

    return { html };
  }

  /**
   * Simulate email sending (replace with actual email service)
   * You can integrate with:
   * - EmailJS (free, works with any hosting)
   * - Your cPanel email + backend service
   * - SendGrid, Mailgun, etc.
   */
  private static async simulateEmailSending(emailData: any): Promise<void> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Log email for demonstration
    console.log('📧 Email would be sent:', {
      to: emailData.to_email,
      subject: emailData.subject,
      timestamp: new Date().toISOString()
    });
    
    // In production, replace this with actual email sending:
    /*
    // Example with EmailJS:
    await emailjs.send(
      'your_service_id',
      'your_template_id',
      emailData,
      'your_public_key'
    );
    
    // Example with fetch to your backend:
    await fetch('/api/send-email', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(emailData)
    });
    */
  }
}

// Export for use in components
export default EmailService;
