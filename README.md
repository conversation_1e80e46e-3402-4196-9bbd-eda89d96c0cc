# DOTPY Academy - Education Platform Website

A professional education platform website built with Next.js, React, and Tailwind CSS, designed for DOTPY Academy - a coding, robotics, and machine learning education center.

## 🚀 Features

### Core Functionality
- **Responsive Design**: Mobile-first approach with modern UI/UX
- **Dynamic Routing**: Next.js app router with dynamic course pages
- **Interactive Components**: Framer Motion animations and smooth transitions
- **Course Management**: Comprehensive course catalog with filtering and search
- **Student Projects**: Showcase gallery for student and academy projects
- **Contact & Registration**: Integrated contact forms and course registration

### Pages & Components
1. **Homepage** - Hero section, featured courses, testimonials
2. **Courses** - Course listing with filters and search
3. **Course Details** - Individual course pages with expandable syllabus
4. **Projects** - Student project showcase with modal views
5. **About Us** - Mission statement and instructor information
6. **Schedule & Pricing** - Class schedules and pricing plans
7. **Contact/Register** - Contact forms and registration

### Technical Features
- **SEO Optimized**: Next.js metadata and Open Graph tags
- **Performance**: Optimized images and lazy loading
- **Accessibility**: Semantic HTML and ARIA labels
- **Modern UI**: Tailwind CSS with custom design system
- **Animations**: Smooth page transitions and micro-interactions

## 🛠️ Tech Stack

- **Frontend**: React 18 + Next.js 14
- **Styling**: Tailwind CSS + Custom CSS
- **Animations**: Framer Motion
- **Icons**: Lucide React
- **TypeScript**: Full type safety
- **Deployment**: Vercel ready

## 📁 Project Structure

```
dotpy-academy/
├── app/                    # Next.js app directory
│   ├── layout.tsx         # Root layout
│   ├── page.tsx           # Homepage
│   ├── courses/           # Course pages
│   ├── projects/          # Projects page
│   ├── about/             # About page
│   ├── schedule-pricing/  # Schedule & pricing
│   └── contact/           # Contact page
├── components/             # Reusable components
│   ├── Navbar.tsx         # Navigation component
│   ├── Hero.tsx           # Hero section
│   ├── CourseCard.tsx     # Course display card
│   ├── FeaturedCourses.tsx # Featured courses section
│   ├── WhyChooseUs.tsx    # Why choose us section
│   ├── Testimonials.tsx   # Student testimonials
│   └── Footer.tsx         # Footer component
├── lib/                    # Data and utilities
│   └── data.ts            # Course and project data
├── public/                 # Static assets
├── styles/                 # Global styles
└── package.json            # Dependencies and scripts
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd dotpy-academy
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Run the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### Build for Production

```bash
npm run build
npm start
```

## 📚 Course Structure

### Python Programming (3 Levels)
- **Beginner** (Age 6-12): Basics, variables, loops, simple games
- **Intermediate** (Age 12-16): Functions, data structures, mini-projects
- **Advanced** (Age 16-20): OOP, libraries, data visualization

### Robotics (2 Levels)
- **Starter**: Microcontrollers, sensors, basic movement
- **Advanced**: IoT, computer vision, AI integration

### Machine Learning & Algorithms
- **Intermediate** (Age 16-20): ML basics, Python libraries, real-world projects

## 🎨 Design System

### Color Palette
- **Primary**: Blue (#3B82F6) - Main brand color
- **Secondary**: Gray (#64748B) - Text and UI elements
- **Accent**: Red (#EF4444) - Call-to-action and highlights

### Typography
- **Font**: Inter (Google Fonts)
- **Weights**: 300, 400, 500, 600, 700

### Components
- **Buttons**: Primary, secondary, and accent variants
- **Cards**: Consistent shadow and border radius
- **Forms**: Accessible input fields with validation
- **Navigation**: Sticky header with dropdown menus

## 🔧 Customization

### Adding New Courses
1. Update `lib/data.ts` with course information
2. Add course images to public directory
3. Update course categories and filters

### Modifying Styles
1. Edit `tailwind.config.js` for theme customization
2. Modify `app/globals.css` for custom CSS
3. Update component-specific styles in individual files

### Adding New Pages
1. Create new directory in `app/` folder
2. Add `page.tsx` file with page content
3. Update navigation in `components/Navbar.tsx`

## 📱 Responsive Design

- **Mobile**: 320px - 768px
- **Tablet**: 768px - 1024px
- **Desktop**: 1024px+

## 🚀 Deployment

### Vercel (Recommended)
1. Connect your GitHub repository to Vercel
2. Vercel will automatically detect Next.js
3. Deploy with zero configuration

### Other Platforms
- **Netlify**: Compatible with Next.js static export
- **AWS Amplify**: Full-stack deployment support
- **Traditional hosting**: Build and upload static files

## 🔒 Environment Variables

Create a `.env.local` file for local development:

```env
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_CONTACT_EMAIL=<EMAIL>
```

## 📊 Performance

- **Lighthouse Score**: 90+ on all metrics
- **Core Web Vitals**: Optimized for user experience
- **Image Optimization**: Next.js Image component with lazy loading
- **Bundle Size**: Optimized with tree shaking and code splitting

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For support and questions:
- **Email**: <EMAIL>
- **Phone**: +****************
- **WhatsApp**: [Contact via WhatsApp](https://wa.me/15551234567)

## 🙏 Acknowledgments

- **Next.js Team** for the amazing framework
- **Tailwind CSS** for the utility-first CSS framework
- **Framer Motion** for smooth animations
- **Unsplash** for high-quality images
- **Polok Poddar** for the vision and expertise

---

**Built with ❤️ for DOTPY Academy**

