# 📧 Email Auto-Reply Setup Guide for dotPY Academy

## 🎯 Overview
This guide shows you how to set up automatic email replies for your contact form using EmailJS - a free service that works perfectly with cPanel hosting.

## 🚀 Quick Setup (5 minutes)

### Step 1: Create EmailJS Account
1. Go to [EmailJS.com](https://www.emailjs.com/)
2. Sign up for a FREE account
3. Verify your email address

### Step 2: Connect Your Email Service
1. In EmailJS dashboard, go to "Email Services"
2. Click "Add New Service"
3. Choose your email provider:
   - **Gmail** (easiest for testing)
   - **Outlook/Hotmail**
   - **Custom SMTP** (for your cPanel email)

#### For cPanel Email (Recommended):
- Service: "Custom SMTP"
- SMTP Server: `mail.yourdomain.com` (replace with your domain)
- Port: `587` (or `465` for SSL)
- Username: `<EMAIL>` (your cPanel email)
- Password: Your email password
- Security: TLS/STARTTLS

### Step 3: Create Email Templates

#### Template 1: Auto-Reply to Users
```html
Subject: Thank you for contacting dotPY Academy! 🎓

Hi {{to_name}}!

Thank you for reaching out to dotPY Academy. We've received your inquiry and our team will get back to you within 24 hours.

📋 Your Inquiry Details:
Name: {{user_name}}
Email: {{user_email}}
Age: {{user_age}}
Interested Course: {{interested_course}}
Message: {{user_message}}

🚀 What's Next?
• 📞 Our team will call you within 24 hours
• 💬 We'll discuss your learning goals
• 📅 Schedule a FREE demo class if interested
• 🎯 Get personalized course recommendations

📞 Contact Information:
Phone: +8801770065234
Email: <EMAIL>
Address: Gulshan 1, Dhaka, Bangladesh

Best regards,
Polok Poddar
Founder & Lead Instructor
dotPY Academy
```

#### Template 2: Admin Notification
```html
Subject: 🔔 New Contact Form Submission from {{user_name}}

📝 Contact Details:
👤 Name: {{user_name}}
📧 Email: {{user_email}}
🎂 Age: {{user_age}}
📚 Interested Course: {{interested_course}}
💬 Message: {{user_message}}
⏰ Submitted: {{submission_time}}

🎯 Recommended Actions:
• 📞 Call {{user_name}} within 24 hours
• 📧 Send personalized course information
• 📅 Offer FREE demo class registration
• 💰 Discuss pricing and payment options

Reply directly to this email to respond to {{user_name}}.
```

### Step 4: Update Your Website Code

1. Install EmailJS in your project:
```bash
npm install @emailjs/browser
```

2. Replace the email service code in `lib/email-service.ts`:

```typescript
import emailjs from '@emailjs/browser';

// Your EmailJS configuration
const EMAILJS_SERVICE_ID = 'your_service_id';
const EMAILJS_USER_TEMPLATE_ID = 'your_user_template_id';
const EMAILJS_ADMIN_TEMPLATE_ID = 'your_admin_template_id';
const EMAILJS_PUBLIC_KEY = 'your_public_key';

// Initialize EmailJS
emailjs.init(EMAILJS_PUBLIC_KEY);

export class EmailService {
  static async sendContactFormEmails(formData: ContactFormData): Promise<EmailResponse> {
    try {
      // Send auto-reply to user
      await emailjs.send(
        EMAILJS_SERVICE_ID,
        EMAILJS_USER_TEMPLATE_ID,
        {
          to_name: formData.name,
          to_email: formData.email,
          user_name: formData.name,
          user_email: formData.email,
          user_age: formData.age,
          interested_course: formData.interestedCourse,
          user_message: formData.message,
          reply_to: '<EMAIL>'
        }
      );

      // Send notification to admin
      await emailjs.send(
        EMAILJS_SERVICE_ID,
        EMAILJS_ADMIN_TEMPLATE_ID,
        {
          to_email: '<EMAIL>',
          user_name: formData.name,
          user_email: formData.email,
          user_age: formData.age,
          interested_course: formData.interestedCourse,
          user_message: formData.message,
          submission_time: new Date().toLocaleString(),
          reply_to: formData.email
        }
      );

      return { success: true, message: 'Emails sent successfully' };
    } catch (error) {
      console.error('Email sending failed:', error);
      return { success: false, message: 'Failed to send emails' };
    }
  }
}
```

### Step 5: Get Your EmailJS Credentials
1. In EmailJS dashboard:
   - **Service ID**: Found in "Email Services" section
   - **Template IDs**: Found in "Email Templates" section
   - **Public Key**: Found in "Account" > "General"

2. Update your code with these credentials

## 🎯 Alternative: cPanel Email with Backend

If you prefer using your cPanel email directly:

### Option 1: PHP Backend (works with cPanel)
Create `api/send-email.php`:
```php
<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = json_decode(file_get_contents('php://input'), true);
    
    // Auto-reply to user
    $userSubject = "Thank you for contacting dotPY Academy! 🎓";
    $userMessage = "Hi " . $data['name'] . "!\n\nThank you for reaching out...";
    
    mail($data['email'], $userSubject, $userMessage, 
         "From: <EMAIL>\r\nReply-To: <EMAIL>");
    
    // Notification to admin
    $adminSubject = "New Contact Form Submission from " . $data['name'];
    $adminMessage = "Name: " . $data['name'] . "\nEmail: " . $data['email'] . "...";
    
    mail('<EMAIL>', $adminSubject, $adminMessage,
         "From: <EMAIL>\r\nReply-To: " . $data['email']);
    
    echo json_encode(['success' => true]);
}
?>
```

### Option 2: Node.js Backend
If your hosting supports Node.js, use Nodemailer with your cPanel SMTP.

## 🔧 Testing Your Setup

1. **Test with your own email first**
2. **Check spam folders**
3. **Verify both auto-reply and admin notification work**
4. **Test with different email providers**

## 📞 Support

If you need help setting this up:
- **EmailJS Documentation**: https://www.emailjs.com/docs/
- **cPanel Email Settings**: Check your hosting control panel
- **Contact your hosting provider** for SMTP details

## 🎉 Benefits

✅ **Free auto-reply system**
✅ **Works with any hosting (including cPanel)**
✅ **No server-side code needed with EmailJS**
✅ **Professional email templates**
✅ **Instant notifications**
✅ **Easy to maintain**

Your students will receive immediate confirmation emails, and you'll get instant notifications of new inquiries!
