'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Clock, Users, BookOpen, Calendar } from 'lucide-react';
import { courses } from '../../../lib/data';
import Psychology<PERSON>ooster from '../../../components/PsychologyBooster';
import EnrollmentForm from '../../../components/EnrollmentForm';
import DemoClassForm from '../../../components/DemoClassForm';

interface CourseDetailsClientProps {
  course: any;
}

export default function CourseDetailsClient({ course }: CourseDetailsClientProps) {
  const [isEnrollmentFormOpen, setIsEnrollmentFormOpen] = useState(false);
  const [isDemoFormOpen, setIsDemoFormOpen] = useState(false);

  const handleEnrollClick = () => {
    setIsEnrollmentFormOpen(true);
  };

  const handleDemoClick = () => {
    setIsDemoFormOpen(true);
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'python':
        return 'bg-blue-100 text-blue-800';
      case 'robotics':
        return 'bg-green-100 text-green-800';
      case 'ml':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'python':
        return '🐍';
      case 'robotics':
        return '🤖';
      case 'ml':
        return '⚡';
      default:
        return '📚';
    }
  };

  // Get related courses (same category, different level)
  const relatedCourses = courses
    .filter(c => c.category === course.category && c.id !== course.id)
    .slice(0, 3);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Course Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Course Image */}
            <div className="relative">
              <img
                src={course.image}
                alt={course.title}
                className="w-full h-80 object-cover rounded-2xl shadow-lg"
              />
              {course.featured && (
                <div className="absolute top-4 left-4 bg-accent-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                  Featured
                </div>
              )}
              <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium text-secondary-700">
                {getCategoryIcon(course.category)}
              </div>
            </div>

            {/* Course Info */}
            <div>
              <div className="mb-4">
                <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${getCategoryColor(course.category)}`}>
                  {course.category.toUpperCase()}
                </span>
              </div>
              
              <h1 className="text-4xl md:text-5xl font-bold text-secondary-900 mb-4">
                {course.title}
              </h1>
              
              <p className="text-lg text-secondary-600 mb-6">
                {course.description}
              </p>

              {/* Course Details */}
              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="flex items-center text-secondary-600">
                  <Clock className="h-5 w-5 mr-2" />
                  <span>{course.duration}</span>
                </div>
                <div className="flex items-center text-secondary-600">
                  <Users className="h-5 w-5 mr-2" />
                  <span>{course.ageGroup}</span>
                </div>
                <div className="flex items-center text-secondary-600">
                  <BookOpen className="h-5 w-5 mr-2" />
                  <span>{course.level} Level</span>
                </div>
                <div className="flex items-center text-secondary-600">
                  <Calendar className="h-5 w-5 mr-2" />
                  <span>2 classes/week</span>
                </div>
              </div>

              {/* Price and Action */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-6">
                  <div>
                    <div className="flex flex-col">
                      <div className="text-3xl font-bold text-green-600">৳{course.price.toLocaleString()}</div>
                      <div className="text-lg text-gray-500 line-through">৳{course.originalPrice.toLocaleString()}</div>
                      <div className="text-sm text-green-600 font-medium">Save ৳{(course.originalPrice - course.price).toLocaleString()}</div>
                    </div>
                    <div className="text-secondary-500">
                      BDT (Bangladeshi Taka)
                    </div>
                  </div>
                </div>

                {/* Contact Information */}
                <div className="space-y-4">
                  <button
                    onClick={handleEnrollClick}
                    className="w-full bg-gradient-to-r from-green-600 to-emerald-600 text-white py-4 px-6 rounded-lg text-center hover:from-green-700 hover:to-emerald-700 transition-all duration-300 transform hover:scale-105 shadow-lg"
                  >
                    <div className="font-semibold text-lg">🚀 Enroll Now - Save ৳{(course.originalPrice - course.price).toLocaleString()}</div>
                    <div className="text-sm mt-1">Call: +880 177 006 5234</div>
                  </button>

                  <button
                    onClick={handleDemoClick}
                    className="w-full bg-gradient-to-r from-yellow-500 to-orange-500 text-white py-3 px-6 rounded-lg text-center hover:from-yellow-600 hover:to-orange-600 transition-all duration-300 transform hover:scale-105 shadow-lg"
                  >
                    <div className="font-semibold">⭐ Book FREE Demo Class</div>
                  </button>

                  <div className="text-center">
                    <div className="text-sm text-gray-600 mb-2">
                      Or pay ৳{course.paymentOptions.monthlyAmount?.toLocaleString()}/month
                    </div>
                    <a href="/contact" className="text-blue-600 hover:underline font-medium">
                      Contact us for enrollment →
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Course Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid lg:grid-cols-3 gap-12">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Syllabus Section */}
            <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8 mb-8">
              <h2 className="text-2xl font-bold text-secondary-900 mb-6 flex items-center">
                <BookOpen className="h-6 w-6 mr-3 text-primary-600" />
                Course Syllabus
              </h2>
              
              <div className="space-y-4">
                {course.syllabus.map((week: any, index: number) => (
                  <div key={index} className="border border-gray-200 rounded-lg overflow-hidden">
                     <div className="w-full px-6 py-4 bg-gray-50">
                       <div className="flex items-center">
                         <div className="w-8 h-8 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-sm font-medium mr-4">
                           {week.classNumber}
                         </div>
                         <div>
                           <h3 className="font-semibold text-secondary-900">{week.title}</h3>
                           <p className="text-sm text-secondary-600">{week.description}</p>
                         </div>
                       </div>
                     </div>

                     <div className="px-6 py-4 bg-white border-t border-gray-200">
                        <h4 className="font-medium text-secondary-900 mb-3">Topics Covered:</h4>
                        <ul className="space-y-2">
                          {week.topics.map((topic: string, topicIndex: number) => (
                            <li key={topicIndex} className="flex items-start">
                              <div className="w-2 h-2 bg-primary-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                              <span className="text-secondary-700">{topic}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Course Features */}
            <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-8">
              <h2 className="text-2xl font-bold text-secondary-900 mb-6">
                What You'll Learn
              </h2>
              
              <div className="grid md:grid-cols-2 gap-6">
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center mr-3 mt-0.5">
                    ✓
                  </div>
                  <span className="text-secondary-700">Hands-on projects and real-world applications</span>
                </div>
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center mr-3 mt-0.5">
                    ✓
                  </div>
                  <span className="text-secondary-700">Expert instruction and personalized feedback</span>
                </div>
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center mr-3 mt-0.5">
                    ✓
                  </div>
                  <span className="text-secondary-700">Certificate of completion</span>
                </div>
                <div className="flex items-start">
                  <div className="w-6 h-6 bg-green-100 text-green-600 rounded-full flex items-center justify-center mr-3 mt-0.5">
                    ✓
                  </div>
                  <span className="text-secondary-700">Lifetime access to course materials</span>
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            {/* Course Summary Card */}
            <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6 mb-6 sticky top-24">
              <h3 className="text-lg font-semibold text-secondary-900 mb-4">
                Course Summary
              </h3>
              
              <div className="space-y-3 mb-6">
                <div className="flex justify-between">
                  <span className="text-secondary-600">Duration:</span>
                  <span className="font-medium">{course.duration}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-secondary-600">Level:</span>
                  <span className="font-medium">{course.level}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-secondary-600">Age Group:</span>
                  <span className="font-medium">{course.ageGroup}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-secondary-600">Classes:</span>
                  <span className="font-medium">24 sessions</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-secondary-600">Schedule:</span>
                  <span className="font-medium">Fri & Sat</span>
                </div>
              </div>

              <div className="border-t border-gray-200 pt-4 mb-6">
                <div className="text-2xl font-bold text-green-600 mb-2">
                  ৳{course.price.toLocaleString()}
                </div>
                <div className="text-lg text-gray-500 line-through mb-1">
                  ৳{course.originalPrice.toLocaleString()}
                </div>
                <div className="text-sm text-green-600 font-medium mb-4">
                  Save ৳{(course.originalPrice - course.price).toLocaleString()}
                </div>
                <div className="text-sm text-secondary-500 mb-4">
                  BDT (Bangladeshi Taka)
                </div>
                <button 
                  onClick={handleEnrollClick}
                  className="w-full btn-primary"
                >
                  Enroll Now
                </button>
              </div>

              <div className="text-center">
                <p className="text-sm text-secondary-500">
                  Need help? <a href="/contact" className="text-primary-600 hover:underline">Contact us</a>
                </p>
              </div>
            </div>

            {/* Related Courses */}
            {relatedCourses.length > 0 && (
              <div className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-secondary-900 mb-4">
                  Related Courses
                </h3>
                <div className="space-y-4">
                  {relatedCourses.map((relatedCourse) => (
                    <div key={relatedCourse.id} className="border border-gray-200 rounded-lg p-4">
                      <h4 className="font-medium text-secondary-900 mb-2">
                        {relatedCourse.title}
                      </h4>
                      <p className="text-sm text-secondary-600 mb-3">
                        {relatedCourse.description.substring(0, 80)}...
                      </p>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-secondary-500">
                          {relatedCourse.level}
                        </span>
                        <a
                          href={`/courses/${relatedCourse.id}`}
                          className="text-primary-600 hover:underline text-sm font-medium"
                        >
                          View Details →
                        </a>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 🧠 PSYCHOLOGY BOOSTER SECTION */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-secondary-900 mb-4">
            Why Choose dotPY Academy?
          </h2>
          <p className="text-lg text-secondary-600 max-w-2xl mx-auto">
            Join hundreds of families who trust us to transform their children into confident young programmers.
          </p>
        </div>
        <PsychologyBooster course={course} />
      </div>

      {/* Enrollment Form */}
      <EnrollmentForm
        isOpen={isEnrollmentFormOpen}
        onClose={() => setIsEnrollmentFormOpen(false)}
        selectedCourse={course}
      />

      {/* Demo Class Form */}
      <DemoClassForm
        isOpen={isDemoFormOpen}
        onClose={() => setIsDemoFormOpen(false)}
      />

    </div>
  );
}
